<!DOCTYPE html>
<html>
<head>
  <title>Modern UI Preview - mDNSResponder</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="styles-additions.css">
  <style>
    /* Additional preview styles */
    body {
      margin: 20px;
      background: linear-gradient(135deg, #0F1419 0%, #0A0E14 100%);
    }
    .preview-container {
      max-width: 1200px;
      margin: 0 auto;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
    }
    .demo-message {
      background: var(--glass-surface);
      padding: 20px;
      margin: 20px 0;
      border-radius: 12px;
      border: var(--glass-border);
      text-align: center;
      color: var(--light-text);
    }
  </style>
</head>
<body>
  <div class="demo-message">
    <h2>🎨 Enhanced UI Preview</h2>
    <p>✨ <strong>New Features:</strong> Tab switching indicators • Code copy buttons • Onscreen keyboard paste functionality</p>
    <p>This preview showcases the modernized glassmorphism design with enhanced visual hierarchy and improved interactions.</p>
  </div>

  <div class="preview-container">
    <div class="app-container">
      <!-- Header Section -->
      <header class="app-header">
        <div class="logo-area">
          <div class="logo-icon">
            <i class="fas fa-code"></i>
          </div>
          <h3>Interview Cracker</h3>
          <div class="shortcut-hints">
            <span><i class="fas fa-keyboard"></i> Toggle: Ctrl/Cmd+Shift+T</span>
            <span><i class="fas fa-arrows-alt"></i> Resize: Ctrl+Shift + arrow keys</span>
            <span><i class="fas fa-camera"></i> Screenshot: Ctrl+Shift+C</span>
            <span><i class="fas fa-cogs"></i> Process: Ctrl+Shift+P</span>
          </div>
        </div>
        <div class="header-controls">
          <button class="minimize-btn" title="Minimize Window">
            <i class="fas fa-minus"></i>
          </button>
          <button class="close-btn" title="Close Window">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </header>

      <!-- Tabs Navigation -->
      <nav class="tabs-nav">
        <div class="tab active" data-tab="assistant">
          <i class="fas fa-comment-dots"></i> <span>Coding</span>
        </div>
        <div class="tab" data-tab="general">
          <i class="fas fa-comments"></i> <span>General Chat</span>
        </div>
        <div class="tab" data-tab="system-design">
          <i class="fas fa-project-diagram"></i> <span>System Design</span>
        </div>
        <div class="tab" data-tab="audio">
          <i class="fas fa-microphone"></i> <span>Audio</span>
        </div>
        <div class="tab" data-tab="shortcuts">
          <i class="fas fa-keyboard"></i> <span>Shortcuts</span>
        </div>
      </nav>

      <!-- Main Content Area -->
      <main class="content-area">
        <!-- Assistant Tab -->
        <div class="tab-content active" id="assistant-tab">
          <div class="chat-interface">
            <!-- Chat Messages Area -->
            <div class="chat-messages-container">
              <div class="chat-header">
                <span class="chat-title">AI Assistant</span>
                <span class="chat-status status-indicator online">
                  <i class="fas fa-circle"></i> Ready
                </span>
              </div>
              <div class="chat-messages">
                <div class="message ai-message visible">
                  <div class="message-content">
                    Hello! I'm here to help with your coding interviews. The new UI features glassmorphism effects, improved typography, and enhanced visual hierarchy for a better user experience.
                  </div>
                  <div class="message-time">Now</div>
                </div>
                <div class="message user-message visible">
                  <div class="message-content">
                    This new design looks amazing! The glassmorphism effects and modern color palette really enhance the user experience.
                  </div>
                  <div class="message-time">Now</div>
                </div>
                <div class="message ai-message visible">
                  <div class="message-content markdown-content">
                    Thank you! The modernized UI includes:
                    <br>• Glassmorphism with frosted glass effects
                    <br>• Enhanced color palette with better contrast
                    <br>• Smooth animations and micro-interactions
                    <br>• Improved responsive design
                    <br>• Better accessibility with focus states
                    <br>• <strong>NEW:</strong> Copy buttons for code blocks
                    <br>• <strong>NEW:</strong> Paste functionality in onscreen keyboard

                    <p>Here's a sample code block with copy functionality:</p>
                    <pre><code class="language-python">def binary_search(arr, target):
    left, right = 0, len(arr) - 1

    while left <= right:
        mid = (left + right) // 2
        if arr[mid] == target:
            return mid
        elif arr[mid] < target:
            left = mid + 1
        else:
            right = mid - 1

    return -1</code><button class="copy-code-btn">Copy</button></pre>
                  </div>
                  <div class="message-time">Now</div>
                </div>
              </div>
            </div>

            <!-- Chat Input Area -->
            <div class="chat-input-container">
              <div class="chat-toolbox">
                <!-- Language Option Group -->
                <div class="option-group">
                  <div class="option-label">Language:</div>
                  <div class="option-buttons">
                    <label class="option-btn selected">
                      <input type="radio" name="language" value="Python" class="option-radio" checked>
                      <span>Python</span>
                    </label>
                    <label class="option-btn">
                      <input type="radio" name="language" value="JavaScript" class="option-radio">
                      <span>JavaScript</span>
                    </label>
                    <label class="option-btn">
                      <input type="radio" name="language" value="Java" class="option-radio">
                      <span>Java</span>
                    </label>
                    <label class="option-btn">
                      <input type="radio" name="language" value="C++" class="option-radio">
                      <span>C++</span>
                    </label>
                  </div>
                </div>

                <!-- Model Option Group -->
                <div class="option-group">
                  <div class="option-label">Model:</div>
                  <div class="option-buttons">
                    <label class="option-btn selected">
                      <input type="radio" name="model" value="gpt-4o" class="option-radio" checked>
                      <span>gpt-4o</span>
                    </label>
                    <label class="option-btn">
                      <input type="radio" name="model" value="deepseekR1" class="option-radio">
                      <span>deepseekR1</span>
                    </label>
                    <label class="option-btn">
                      <input type="radio" name="model" value="o3-mini" class="option-radio">
                      <span>o3-mini</span>
                    </label>
                  </div>
                </div>
              </div>

              <div class="input-row">
                <textarea class="chat-input" placeholder="Type your question here..." rows="1">Try the new modern UI!</textarea>
                <button class="send-button">
                  <i class="fas fa-paper-plane"></i>
                </button>
              </div>

              <div class="chat-actions">
                <button class="action-btn" title="Clear Conversation">
                  <i class="fas fa-trash-alt"></i> <span>Clear</span>
                </button>
                <button class="action-btn" title="Get Optimized Code">
                  <i class="fas fa-bolt"></i> <span>Optimize</span>
                </button>
                <button class="action-btn" title="Capture Screen">
                  <i class="fas fa-camera"></i> <span>Capture</span>
                </button>
                <button class="action-btn" title="Process Image">
                  <i class="fas fa-cogs"></i> <span>Process</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>

      <!-- Status Bar -->
      <footer class="status-bar">
        <div class="status-info">
          <i class="fas fa-info-circle"></i>
          <span>Modern UI Active • Glassmorphism Enabled</span>
        </div>
        <div class="app-version">
          v2.0.0
        </div>
      </footer>
    </div>
  </div>

  <!-- Floating notification demo -->
  <div class="floating-notification notification-success show">
    <i class="fas fa-check-circle"></i>
    UI successfully modernized with glassmorphism effects!
  </div>

  <script>
    // Simple demo interactions
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', function() {
        document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
        this.classList.add('active');
      });
    });

    document.querySelectorAll('.option-btn').forEach(btn => {
      btn.addEventListener('click', function() {
        const group = this.closest('.option-buttons');
        group.querySelectorAll('.option-btn').forEach(b => b.classList.remove('selected'));
        this.classList.add('selected');
      });
    });

    // Copy button functionality
    document.querySelectorAll('.copy-code-btn').forEach(btn => {
      btn.addEventListener('click', async function() {
        const codeBlock = this.parentElement.querySelector('code');
        const codeText = codeBlock.textContent;

        try {
          await navigator.clipboard.writeText(codeText);
          this.textContent = 'Copied!';
          this.classList.add('copied');

          // Show success notification
          const notification = document.querySelector('.floating-notification');
          notification.innerHTML = '<i class="fas fa-check-circle"></i>Code copied to clipboard!';
          notification.className = 'floating-notification notification-success show';

          setTimeout(() => {
            this.textContent = 'Copy';
            this.classList.remove('copied');
          }, 2000);

          setTimeout(() => {
            notification.classList.remove('show');
          }, 3000);

        } catch (error) {
          console.warn('Failed to copy:', error);

          // Fallback: try execCommand
          try {
            const range = document.createRange();
            range.selectNodeContents(codeBlock);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);

            const copySuccess = document.execCommand('copy');
            if (copySuccess) {
              this.textContent = 'Copied!';
              this.classList.add('copied');

              const notification = document.querySelector('.floating-notification');
              notification.innerHTML = '<i class="fas fa-check-circle"></i>Code copied to clipboard!';
              notification.className = 'floating-notification notification-success show';

              setTimeout(() => {
                this.textContent = 'Copy';
                this.classList.remove('copied');
              }, 2000);

              setTimeout(() => {
                notification.classList.remove('show');
              }, 3000);
            } else {
              this.textContent = 'Selected';
              const notification = document.querySelector('.floating-notification');
              notification.innerHTML = '<i class="fas fa-info-circle"></i>Code selected - press Ctrl+C to copy';
              notification.className = 'floating-notification notification-warning show';

              setTimeout(() => {
                this.textContent = 'Copy';
              }, 3000);

              setTimeout(() => {
                notification.classList.remove('show');
              }, 5000);
            }
          } catch (fallbackError) {
            this.textContent = 'Failed';
            setTimeout(() => {
              this.textContent = 'Copy';
            }, 2000);
          }
        }
      });
    });

    // Auto-hide initial notification after 5 seconds
    setTimeout(() => {
      const notification = document.querySelector('.floating-notification');
      if (notification.innerHTML.includes('successfully modernized')) {
        notification.classList.remove('show');
      }
    }, 5000);
  </script>
</body>
</html>
