# ⌨️ InterviewCracker Shortcuts Guide

## 🧪 **Testing Shortcuts**

### **Test if Global Shortcuts Work**
- **`Ctrl+Shift+Z`** - Test shortcut (shows alert if working)

## 🛡️ **Focus-Safe Mode**

### **Toggle Focus-Safe Mode**
- **`Ctrl+Shift+F`** - Toggle between safe/unsafe mode
- **Click green/orange indicator** - Alternative toggle method

## 🖼️ **Helper Window Controls**

### **Visibility**
- **`Ctrl+Shift+I`** - Toggle helper window visibility
- **`Ctrl+Shift+H`** - Hide helper window
- **`Ctrl+Shift+S`** - Show helper window
- **`Ctrl+Shift+T`** - Toggle window opacity (transparent/opaque)

### **Window Movement**
- **`Ctrl+Left`** - Move window left
- **`Ctrl+Right`** - Move window right
- **`Ctrl+Up`** - Move window up
- **`Ctrl+Down`** - Move window down

### **Window Resizing**
- **`Ctrl+Plus`** - Enlarge window (or Ctrl+NumPad+)
- **`Ctrl+Minus`** - Shrink window (or Ctrl+NumPad-)

### **Directional Resizing**
- **`Ctrl+Shift+Left`** - Decrease window width
- **`Ctrl+Shift+Right`** - Increase window width
- **`Ctrl+Shift+Up`** - Decrease window height
- **`Ctrl+Shift+Down`** - Increase window height

## 🎬 **Recording Controls**
- **`Ctrl+Shift+R`** - Start/Stop recording

## 📸 **Capture & Processing**
- **`Ctrl+Shift+C`** - Trigger screen capture
- **`Ctrl+Shift+P`** - Trigger processing

## 🖥️ **Dashboard Controls**
- **`Ctrl+Shift+D`** - Show dashboard window
- **`Ctrl+Shift+M`** - Minimize dashboard

## 🔧 **Troubleshooting Shortcuts**

### **If Shortcuts Don't Work:**

1. **Test Basic Functionality**
   - Try `Ctrl+Shift+Z` first
   - Should show an alert if global shortcuts work

2. **Check Console Output**
   - Look for registration messages:
   - ✅ "Test shortcut registered successfully"
   - ✅ "Focus-Safe Mode shortcut registered"
   - ❌ "Failed to register" messages

3. **Common Issues**
   - **Other apps using shortcuts** - Try different combinations
   - **Admin permissions** - Run as administrator if needed
   - **Antivirus blocking** - Add InterviewCracker to exceptions

4. **Alternative Methods**
   - **Click indicators** - Use visual buttons instead
   - **Menu options** - Right-click for context menus
   - **Mouse controls** - Drag to move, resize handles

### **Shortcut Conflicts**

If a shortcut doesn't work, it might be used by:
- **Windows system shortcuts**
- **Other applications** (Discord, Slack, etc.)
- **Browser extensions**
- **Antivirus software**

### **Platform Differences**

| Windows/Linux | macOS | Action |
|---------------|-------|--------|
| `Ctrl+Shift+F` | `Cmd+Shift+F` | Focus-Safe toggle |
| `Ctrl+Left` | `Cmd+Left` | Move left |
| `Ctrl+Right` | `Cmd+Right` | Move right |
| `Ctrl+Up` | `Cmd+Up` | Move up |
| `Ctrl+Down` | `Cmd+Down` | Move down |
| `Ctrl+Plus` | `Cmd+Plus` | Enlarge window |
| `Ctrl+Minus` | `Cmd+Minus` | Shrink window |
| `Ctrl+Shift+Left` | `Cmd+Shift+Left` | Decrease width |
| `Ctrl+Shift+Right` | `Cmd+Shift+Right` | Increase width |
| `Ctrl+Shift+Up` | `Cmd+Shift+Up` | Decrease height |
| `Ctrl+Shift+Down` | `Cmd+Shift+Down` | Increase height |
| `Ctrl+Shift+I` | `Cmd+Shift+I` | Toggle visibility |

## 🎯 **Most Important Shortcuts**

1. **`Ctrl+Shift+Z`** - Test if shortcuts work
2. **`Ctrl+Shift+F`** - Toggle Focus-Safe Mode
3. **`Ctrl+Shift+I`** - Show/hide helper
4. **`Ctrl+Arrow`** - Move window around

## 📝 **Quick Setup Test**

1. **Start InterviewCracker**
2. **Press `Ctrl+Shift+Z`** - Should show alert
3. **Press `Ctrl+Shift+F`** - Should toggle focus-safe indicator
4. **Press `Ctrl+Shift+I`** - Should hide/show helper window

If all three work, your shortcuts are properly configured!

## 🔍 **Debug Information**

Check the console for these messages:
```
🔧 Starting global shortcut registration...
✅ Test shortcut (Ctrl+Shift+Z) registered successfully
✅ Focus-Safe Mode shortcut (Ctrl+Shift+F) registered
✅ Helper visibility shortcut (Ctrl+Shift+I) registered
```

If you see ❌ messages, those specific shortcuts failed to register.

## 🆘 **Fallback Options**

If global shortcuts don't work:
1. **Use visual controls** - Click buttons and indicators
2. **Use local shortcuts** - Work only when helper has focus
3. **Use mouse controls** - Drag, resize, right-click menus
4. **Check system settings** - Disable conflicting shortcuts

---

**Remember**: Global shortcuts work from anywhere, local shortcuts only work when the helper window is focused.
