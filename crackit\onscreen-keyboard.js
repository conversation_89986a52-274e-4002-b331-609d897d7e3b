// Enhanced On-Screen Keyboard for InterviewCracker
// Zero-focus-stealing virtual keyboard with advanced text handling

class OnScreenKeyboard {
  constructor() {
    this.isVisible = false;
    this.targetInput = null;
    this.keyboard = null;
    this.suggestionsContainer = null;
    this.currentSuggestions = [];
    this.isShiftPressed = false;
    this.isCapsLock = false;
    this.currentText = '';
    this.cursorPosition = 0;

    // AI-powered suggestions
    this.aiSuggestionsEnabled = true;
    this.contextHistory = [];
    this.userTypingPatterns = new Map();
    this.lastAIRequest = 0;
    this.aiRequestDelay = 500; // ms
    this.aiCache = new Map();

    // Enhanced word suggestions database
    this.commonWords = [
      // Programming & Computer Science
      'algorithm', 'algorithms', 'array', 'arrays', 'binary', 'boolean', 'class', 'classes',
      'constructor', 'database', 'databases', 'debug', 'debugging', 'exception', 'exceptions',
      'function', 'functions', 'hash', 'hashing', 'interface', 'interfaces', 'javascript',
      'loop', 'loops', 'method', 'methods', 'object', 'objects', 'parameter', 'parameters',
      'python', 'queue', 'queues', 'recursion', 'recursive', 'stack', 'stacks', 'string',
      'strings', 'variable', 'variables', 'while', 'return', 'returns', 'import', 'export',
      'const', 'let', 'var', 'async', 'await', 'promise', 'promises', 'callback', 'callbacks',

      // Data Structures & Algorithms
      'linkedlist', 'tree', 'trees', 'graph', 'graphs', 'heap', 'heaps', 'sort', 'sorting',
      'search', 'searching', 'traverse', 'traversal', 'node', 'nodes', 'pointer', 'pointers',
      'index', 'indices', 'iterate', 'iteration', 'recursive', 'dynamic', 'programming',
      'greedy', 'backtrack', 'backtracking', 'memoization', 'optimization', 'complexity',

      // Interview & Technical Terms
      'interview', 'interviews', 'question', 'questions', 'answer', 'answers', 'solution',
      'solutions', 'problem', 'problems', 'optimize', 'optimized', 'efficient', 'efficiency',
      'performance', 'scalable', 'scalability', 'design', 'system', 'systems', 'architecture',
      'implementation', 'approach', 'strategy', 'analysis', 'requirement', 'requirements',

      // Common Programming Languages
      'javascript', 'python', 'java', 'cpp', 'csharp', 'golang', 'rust', 'typescript',
      'react', 'angular', 'vue', 'node', 'express', 'django', 'flask', 'spring',

      // Common English Words (Extended)
      'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one',
      'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see',
      'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use',
      'about', 'after', 'again', 'against', 'because', 'before', 'being', 'below', 'between',
      'both', 'during', 'each', 'few', 'from', 'further', 'here', 'into', 'more', 'most',
      'other', 'over', 'same', 'some', 'such', 'than', 'that', 'their', 'them', 'these',
      'they', 'this', 'those', 'through', 'time', 'very', 'what', 'when', 'where', 'which',
      'while', 'with', 'would', 'your', 'could', 'should', 'might', 'will', 'shall'
    ];

    this.init();
  }

  init() {
    this.createKeyboard();
    this.createSuggestionsContainer();
    this.setupEventListeners();
  }

  createKeyboard() {
    this.keyboard = document.createElement('div');
    this.keyboard.id = 'onscreen-keyboard';
    this.keyboard.style.cssText = `
      position: fixed;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.9);
      border-radius: 12px;
      padding: 15px;
      display: none;
      z-index: 10001;
      box-shadow: 0 8px 32px rgba(0,0,0,0.5);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255,255,255,0.1);
    `;

    // Create keyboard layout with proper symbols and paste functionality
    const layout = [
      ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '=', 'Backspace'],
      ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '[', ']'],
      ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ';', "'"],
      ['Shift', 'z', 'x', 'c', 'v', 'b', 'n', 'm', ',', '.', '/', 'Shift'],
      ['Space','Enter', 'Close']
    ];

    layout.forEach(row => {
      const rowDiv = document.createElement('div');
      rowDiv.style.cssText = `
        display: flex;
        gap: 5px;
        margin-bottom: 5px;
        justify-content: center;
      `;

      row.forEach(key => {
        const keyButton = document.createElement('button');

        // Handle display text for special keys with safe fallbacks
        let displayText = key;
        if (key === 'Space') {
          displayText = 'Space';
        } else if (key === 'Backspace') {
          displayText = 'Backspace';
        } else if (key === 'Enter') {
          displayText = 'Enter';
        } else if (key === 'Shift') {
          displayText = 'Shift';
        } else if (key === 'Close') {
          displayText = 'Close';
        } else if (key === 'Paste') {
          displayText = 'Paste';
        }

        keyButton.textContent = displayText;
        keyButton.dataset.key = key;

        // Set appropriate widths
        let width = '40px';
        if (key === 'Space') width = '160px';
        else if (key === 'Backspace') width = '80px';
        else if (key === 'Enter') width = '60px';
        else if (key === 'Close') width = '60px';
        else if (key === 'Shift') width = '60px';
        else if (key === 'Paste') width = '70px';

        // Style the key button with shift state awareness
        const isShiftKey = key === 'Shift';
        const shiftColor = this.isShiftPressed && isShiftKey ? 'rgba(76, 175, 80, 0.3)' : 'rgba(255,255,255,0.1)';

        keyButton.style.cssText = `
          width: ${width};
          height: 40px;
          background: ${shiftColor};
          border: 1px solid rgba(255,255,255,0.2);
          border-radius: 6px;
          color: white;
          font-size: 14px;
          font-weight: bold;
          cursor: pointer;
          transition: all 0.2s ease;
          user-select: none;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;

        keyButton.addEventListener('mouseenter', () => {
          keyButton.style.background = 'rgba(255,255,255,0.2)';
          keyButton.style.transform = 'scale(1.05)';
        });

        keyButton.addEventListener('mouseleave', () => {
          keyButton.style.background = 'rgba(255,255,255,0.1)';
          keyButton.style.transform = 'scale(1)';
        });

        // Prevent focus on keyboard buttons
        keyButton.addEventListener('mousedown', (e) => {
          e.preventDefault();
          e.stopPropagation();
        });

        keyButton.addEventListener('focus', (e) => {
          e.preventDefault();
          e.target.blur();
        });

        keyButton.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          this.handleKeyPress(key);
        });

        rowDiv.appendChild(keyButton);
      });

      this.keyboard.appendChild(rowDiv);
    });

    // Add header with AI toggle
    const header = document.createElement('div');
    header.style.cssText = `
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: white;
      font-size: 12px;
      margin-bottom: 10px;
      opacity: 0.8;
    `;

    const title = document.createElement('span');
    title.textContent = 'On-Screen Keyboard';

    const aiToggle = document.createElement('button');
    aiToggle.textContent = this.aiSuggestionsEnabled ? 'AI: ON' : 'AI: OFF';
    aiToggle.style.cssText = `
      background: ${this.aiSuggestionsEnabled ? 'rgba(76, 175, 80, 0.3)' : 'rgba(255, 255, 255, 0.1)'};
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 4px;
      color: white;
      font-size: 10px;
      padding: 2px 6px;
      cursor: pointer;
      transition: all 0.2s ease;
    `;

    // Prevent focus on AI toggle button
    aiToggle.addEventListener('mousedown', (e) => {
      e.preventDefault();
      e.stopPropagation();
    });

    aiToggle.addEventListener('focus', (e) => {
      e.preventDefault();
      e.target.blur();
    });

    aiToggle.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.aiSuggestionsEnabled = !this.aiSuggestionsEnabled;
      aiToggle.textContent = this.aiSuggestionsEnabled ? 'AI: ON' : 'AI: OFF';
      aiToggle.style.background = this.aiSuggestionsEnabled ? 'rgba(76, 175, 80, 0.3)' : 'rgba(255, 255, 255, 0.1)';
      console.log('AI suggestions toggled:', this.aiSuggestionsEnabled);
    });

    header.appendChild(title);
    header.appendChild(aiToggle);
    this.keyboard.insertBefore(header, this.keyboard.firstChild);

    document.body.appendChild(this.keyboard);
  }

  createSuggestionsContainer() {
    this.suggestionsContainer = document.createElement('div');
    this.suggestionsContainer.id = 'keyboard-suggestions';
    this.suggestionsContainer.style.cssText = `
      position: fixed;
      bottom: 280px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.9);
      border-radius: 8px;
      padding: 8px;
      display: none;
      z-index: 10002;
      box-shadow: 0 4px 16px rgba(0,0,0,0.3);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255,255,255,0.1);
      max-width: 600px;
      flex-wrap: wrap;
      gap: 4px;
    `;

    document.body.appendChild(this.suggestionsContainer);
  }

  setupEventListeners() {
    // Show keyboard when clicking on input fields
    document.addEventListener('click', (e) => {
      if (this.isInputField(e.target)) {
        e.preventDefault();
        e.stopPropagation();
        this.showKeyboard(e.target);
      }
    });

    // Hide keyboard when clicking outside (but not on suggestions)
    document.addEventListener('click', (e) => {
      if (this.isVisible &&
          !this.keyboard.contains(e.target) &&
          !this.suggestionsContainer.contains(e.target) &&
          !this.isInputField(e.target)) {
        this.hideKeyboard();
      }
    });

    // Global shortcut to toggle keyboard
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'K') {
        e.preventDefault();
        e.stopPropagation();
        this.toggleKeyboard();
      }
    });

    // Prevent any focus events on keyboard elements
    document.addEventListener('mousedown', (e) => {
      if (this.keyboard && (this.keyboard.contains(e.target) || this.suggestionsContainer.contains(e.target))) {
        e.preventDefault();
        e.stopPropagation();
      }
    });

    // Monitor input changes for suggestions
    document.addEventListener('input', (e) => {
      if (this.isVisible && e.target === this.targetInput) {
        this.updateCurrentText();
        this.updateSuggestions();
      }
    });
  }

  isInputField(element) {
    return element && (
      element.tagName === 'INPUT' ||
      element.tagName === 'TEXTAREA' ||
      element.contentEditable === 'true'
    );
  }

  // Focus-safe mode check removed

  showKeyboard(targetInput = null) {
    this.targetInput = targetInput || document.getElementById('chat-input');
    this.keyboard.style.display = 'block';
    this.isVisible = true;

    // Highlight the target input
    if (this.targetInput) {
      this.targetInput.style.boxShadow = '0 0 0 2px #4CAF50';
      // Get current text for suggestions
      this.updateCurrentText();
      this.updateSuggestions();
    }

    console.log('📱 On-screen keyboard shown');
  }

  hideKeyboard() {
    this.keyboard.style.display = 'none';
    this.suggestionsContainer.style.display = 'none';
    this.isVisible = false;

    // Remove highlight from target input
    if (this.targetInput) {
      this.targetInput.style.boxShadow = '';
      this.targetInput = null;
    }

    console.log('📱 On-screen keyboard hidden');
  }

  toggleKeyboard() {
    if (this.isVisible) {
      this.hideKeyboard();
    } else {
      this.showKeyboard();
    }
  }

  handleKeyPress(key) {
    if (!this.targetInput) return;

    console.log('Key pressed:', key); // Debug log

    switch (key) {
      case 'Backspace':
        this.handleBackspace();
        break;
      case 'Enter':
        this.handleEnter();
        break;
      case 'Space':
        this.insertText(' '); // Explicitly insert space character
        break;
      case 'Paste':
        this.handlePaste();
        break;
      case 'Shift':
        this.toggleShift();
        return; // Don't trigger input event for shift
      case 'Close':
        this.hideKeyboard();
        return; // Don't trigger input event for close
      default:
        // Handle shift state for character input
        let charToInsert = key;
        if (this.isShiftPressed && key.length === 1) {
          charToInsert = this.getShiftedChar(key);
        }

        // Ensure we're inserting the actual character, not the key name
        if (charToInsert && charToInsert.length > 0) {
          this.insertText(charToInsert);
        }
        break;
    }

    // Update suggestions after text change (only for text-changing operations)
    if (key !== 'Shift' && key !== 'Close') {
      this.updateCurrentText();
      this.updateSuggestions();

      // Trigger input event
      this.targetInput.dispatchEvent(new Event('input', { bubbles: true }));
    }
  }

  insertText(text) {
    if (!this.targetInput || text === null || text === undefined) return;

    // Convert to string and handle special cases
    let textToInsert = String(text);

    // Handle special key cases without normalization
    if (text === ' ' || text === 'Space') {
      textToInsert = ' ';
    } else if (text.length === 1) {
      // For single characters, use as-is
      textToInsert = text;
    } else {
      // Only normalize multi-character strings
      textToInsert = this.normalizeText(text);
    }

    try {
      if (this.isStandardInput()) {
        this.insertIntoStandardInput(textToInsert);
      } else if (this.isContentEditable()) {
        this.insertIntoContentEditable(textToInsert);
      }

      // Update internal state
      this.updateCursorPosition();
      this.updateCurrentText();

    } catch (error) {
      console.warn('Error inserting text:', error);
      this.fallbackInsertText(textToInsert);
    }
  }

  normalizeText(text) {
    // Simple text normalization without aggressive filtering
    if (typeof text !== 'string') {
      text = String(text);
    }

    // Only remove truly problematic characters, keep normal text intact
    return text
      .replace(/[\u200B-\u200D\uFEFF]/g, '') // Remove zero-width characters only
      .replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F]/g, ''); // Remove only control chars, keep \t \n \r
  }

  isStandardInput() {
    return this.targetInput &&
           (this.targetInput.tagName === 'INPUT' || this.targetInput.tagName === 'TEXTAREA');
  }

  isContentEditable() {
    return this.targetInput &&
           (this.targetInput.contentEditable === 'true' ||
            this.targetInput.getAttribute('contenteditable') === 'true');
  }

  insertIntoStandardInput(text) {
    const element = this.targetInput;
    const start = element.selectionStart || 0;
    const end = element.selectionEnd || 0;
    const currentValue = element.value || '';

    // Create new value with proper text insertion
    const beforeCursor = currentValue.substring(0, start);
    const afterCursor = currentValue.substring(end);
    const newValue = beforeCursor + text + afterCursor;

    // Set the new value
    element.value = newValue;

    // Position cursor after inserted text
    const newCursorPos = start + text.length;
    element.setSelectionRange(newCursorPos, newCursorPos);

    // Ensure the element is focused for proper cursor display
    if (document.activeElement !== element) {
      element.focus();
    }
  }

  insertIntoContentEditable(text) {
    const selection = window.getSelection();

    if (selection.rangeCount === 0) {
      // No selection, focus the element and try again
      this.targetInput.focus();
      if (selection.rangeCount === 0) {
        // Still no selection, append to end
        this.targetInput.textContent += text;
        return;
      }
    }

    const range = selection.getRangeAt(0);

    // Delete any selected content
    range.deleteContents();

    // Create and insert text node
    const textNode = document.createTextNode(text);
    range.insertNode(textNode);

    // Move cursor after inserted text
    range.setStartAfter(textNode);
    range.setEndAfter(textNode);
    range.collapse(true);

    // Update selection
    selection.removeAllRanges();
    selection.addRange(range);
  }

  fallbackInsertText(text) {
    try {
      if (this.targetInput.value !== undefined) {
        this.targetInput.value += text;
      } else if (this.targetInput.textContent !== undefined) {
        this.targetInput.textContent += text;
      } else if (this.targetInput.innerText !== undefined) {
        this.targetInput.innerText += text;
      }
    } catch (error) {
      console.error('All text insertion methods failed:', error);
    }
  }

  handleBackspace() {
    if (!this.targetInput) return;

    try {
      if (this.isStandardInput()) {
        this.backspaceStandardInput();
      } else if (this.isContentEditable()) {
        this.backspaceContentEditable();
      }

      // Update internal state
      this.updateCursorPosition();
      this.updateCurrentText();

    } catch (error) {
      console.warn('Error handling backspace:', error);
      this.fallbackBackspace();
    }
  }

  backspaceStandardInput() {
    const element = this.targetInput;
    const start = element.selectionStart || 0;
    const end = element.selectionEnd || 0;
    const currentValue = element.value || '';

    let newValue;
    let newCursorPos;

    if (start === end) {
      // No selection - delete character before cursor
      if (start > 0) {
        newValue = currentValue.substring(0, start - 1) + currentValue.substring(start);
        newCursorPos = start - 1;
      } else {
        // Already at beginning, nothing to delete
        return;
      }
    } else {
      // Selection exists - delete selected text
      newValue = currentValue.substring(0, start) + currentValue.substring(end);
      newCursorPos = start;
    }

    // Apply changes
    element.value = newValue;
    element.setSelectionRange(newCursorPos, newCursorPos);

    // Ensure focus for proper cursor display
    if (document.activeElement !== element) {
      element.focus();
    }
  }

  backspaceContentEditable() {
    const selection = window.getSelection();

    if (selection.rangeCount === 0) {
      // No selection, try to focus and create one
      this.targetInput.focus();
      if (selection.rangeCount === 0) {
        // Still no selection, try fallback
        this.fallbackBackspace();
        return;
      }
    }

    const range = selection.getRangeAt(0);

    if (range.collapsed) {
      // No selection - delete character before cursor
      const container = range.startContainer;
      const offset = range.startOffset;

      if (container.nodeType === Node.TEXT_NODE) {
        // In text node
        if (offset > 0) {
          const textContent = container.textContent;
          const newText = textContent.substring(0, offset - 1) + textContent.substring(offset);
          container.textContent = newText;

          // Position cursor
          range.setStart(container, offset - 1);
          range.setEnd(container, offset - 1);
        } else {
          // At beginning of text node, need to handle differently
          this.handleComplexBackspace(range);
        }
      } else {
        // In element node
        if (offset > 0) {
          const previousNode = container.childNodes[offset - 1];
          if (previousNode && previousNode.nodeType === Node.TEXT_NODE) {
            const textContent = previousNode.textContent;
            if (textContent.length > 0) {
              previousNode.textContent = textContent.substring(0, textContent.length - 1);
              range.setStart(previousNode, previousNode.textContent.length);
              range.setEnd(previousNode, previousNode.textContent.length);
            }
          }
        }
      }
    } else {
      // Selection exists - delete selected content
      range.deleteContents();
    }

    // Update selection
    selection.removeAllRanges();
    selection.addRange(range);
  }

  handleComplexBackspace(range) {
    // Handle backspace at complex positions (beginning of nodes, etc.)
    try {
      // Try to move to previous character
      if (range.startOffset === 0 && range.startContainer.previousSibling) {
        const prevSibling = range.startContainer.previousSibling;
        if (prevSibling.nodeType === Node.TEXT_NODE && prevSibling.textContent.length > 0) {
          const newLength = prevSibling.textContent.length - 1;
          prevSibling.textContent = prevSibling.textContent.substring(0, newLength);
          range.setStart(prevSibling, newLength);
          range.setEnd(prevSibling, newLength);
        }
      } else {
        // Use execCommand as fallback
        document.execCommand('delete', false);
      }
    } catch (error) {
      console.warn('Complex backspace failed:', error);
      this.fallbackBackspace();
    }
  }

  fallbackBackspace() {
    try {
      // Try execCommand first
      if (document.execCommand('delete', false)) {
        return;
      }
    } catch (error) {
      console.warn('execCommand backspace failed:', error);
    }

    // Manual fallback for standard inputs
    if (this.isStandardInput() && this.targetInput.value) {
      this.targetInput.value = this.targetInput.value.slice(0, -1);
    } else if (this.isContentEditable() && this.targetInput.textContent) {
      this.targetInput.textContent = this.targetInput.textContent.slice(0, -1);
    }
  }

  handleEnter() {
    // Check if this is a chat input and trigger send
    if (this.targetInput.id === 'chat-input') {
      const sendButton = document.querySelector('.send-button, #send-btn, [onclick*="send"]');
      if (sendButton) {
        sendButton.click();
        this.hideKeyboard();
        return;
      }
    }

    this.insertText('\n');
  }

  async handlePaste() {
    if (!this.targetInput) {
      console.warn('📋 No target input for paste operation');
      this.showPasteError('No input selected');
      return;
    }

    console.log('📋 Attempting to paste from clipboard...');

    // Method 1: Try modern Clipboard API first (if available and secure context)
    if (navigator.clipboard && navigator.clipboard.readText && window.isSecureContext) {
      try {
        const clipboardText = await navigator.clipboard.readText();

        if (clipboardText && clipboardText.trim()) {
          const cleanText = this.cleanPastedText(clipboardText);
          this.insertPastedText(cleanText);
          this.showPasteSuccess();
          return;
        } else {
          console.log('📋 Clipboard is empty');
          this.showPasteError('Clipboard is empty');
          return;
        }
      } catch (clipboardError) {
        console.log('📋 Clipboard API failed, trying alternative methods:', clipboardError.message);
      }
    }

    // Method 2: Focus input and listen for paste event (most reliable fallback)
    try {
      this.targetInput.focus();

      // Show instruction for manual paste
      this.showPasteInstruction();

      // Create a promise that resolves when paste is detected
      const pastePromise = new Promise((resolve, reject) => {
        const handlePasteEvent = (e) => {
          try {
            e.preventDefault();

            // Try multiple ways to get clipboard data
            let pastedText = '';

            if (e.clipboardData) {
              pastedText = e.clipboardData.getData('text/plain') ||
                          e.clipboardData.getData('text') ||
                          e.clipboardData.getData('Text');
            } else if (window.clipboardData) {
              pastedText = window.clipboardData.getData('Text') ||
                          window.clipboardData.getData('text');
            }

            if (pastedText) {
              const cleanText = this.cleanPastedText(pastedText);
              this.insertPastedText(cleanText);
              this.showPasteSuccess();
              resolve(true);
            } else {
              reject(new Error('No text data in paste event'));
            }

          } catch (eventError) {
            reject(eventError);
          } finally {
            this.targetInput.removeEventListener('paste', handlePasteEvent);
          }
        };

        this.targetInput.addEventListener('paste', handlePasteEvent);

        // Auto-cleanup after 15 seconds
        setTimeout(() => {
          this.targetInput.removeEventListener('paste', handlePasteEvent);
          reject(new Error('Paste timeout'));
        }, 15000);
      });

      // Wait for paste or timeout
      await pastePromise;

    } catch (pasteError) {
      console.warn('📋 Paste event method failed:', pasteError.message);

      // Method 3: Try to trigger paste programmatically
      try {
        // Create a temporary textarea to capture paste
        const tempTextarea = document.createElement('textarea');
        tempTextarea.style.cssText = `
          position: fixed;
          top: 50%;
          left: 50%;
          width: 1px;
          height: 1px;
          opacity: 0.01;
          pointer-events: none;
        `;

        document.body.appendChild(tempTextarea);
        tempTextarea.focus();

        // Try to execute paste command
        const pasteSuccess = document.execCommand('paste');

        if (pasteSuccess && tempTextarea.value) {
          const cleanText = this.cleanPastedText(tempTextarea.value);
          this.insertPastedText(cleanText);
          this.showPasteSuccess();
        } else {
          throw new Error('execCommand paste failed');
        }

        document.body.removeChild(tempTextarea);

      } catch (execError) {
        console.warn('📋 execCommand paste failed:', execError.message);

        // Method 4: Show manual paste instruction
        this.showPasteInstruction();
      }
    }
  }

  cleanPastedText(text) {
    return text
      .replace(/\r\n/g, '\n')  // Normalize line breaks
      .replace(/\r/g, '\n')    // Handle old Mac line breaks
      .trim();
  }

  insertPastedText(text) {
    // Insert the text
    this.insertText(text);

    // Update internal state
    this.updateCurrentText();
    this.updateSuggestions();

    // Trigger input event
    this.targetInput.dispatchEvent(new Event('input', { bubbles: true }));

    console.log('📋 Successfully pasted text:', text.substring(0, 50) + (text.length > 50 ? '...' : ''));
  }

  showPasteSuccess() {
    const pasteButtons = this.keyboard.querySelectorAll('[data-key="Paste"]');
    pasteButtons.forEach(btn => {
      const originalText = btn.textContent;
      btn.textContent = 'Pasted!';
      btn.style.background = 'rgba(76, 175, 80, 0.5)';
      btn.style.borderColor = 'rgba(76, 175, 80, 0.7)';
      btn.style.color = 'white';

      setTimeout(() => {
        btn.textContent = originalText;
        btn.style.background = 'rgba(255,255,255,0.1)';
        btn.style.borderColor = 'rgba(255,255,255,0.2)';
        btn.style.color = 'white';
      }, 1000);
    });

    console.log('📋 Paste operation completed successfully');
  }

  showPasteInstruction() {
    // Show instruction for manual paste
    const pasteButtons = this.keyboard.querySelectorAll('[data-key="Paste"]');
    pasteButtons.forEach(btn => {
      const originalText = btn.textContent;

      btn.textContent = 'Press Ctrl+V';
      btn.style.background = 'rgba(0, 212, 170, 0.4)';
      btn.style.borderColor = 'rgba(0, 212, 170, 0.6)';
      btn.style.color = 'white';

      setTimeout(() => {
        btn.textContent = originalText;
        btn.style.background = 'rgba(255,255,255,0.1)';
        btn.style.borderColor = 'rgba(255,255,255,0.2)';
        btn.style.color = 'white';
      }, 5000);
    });

    console.log('📋 Showing paste instruction');

    if (typeof showNotification === 'function') {
      showNotification('Input focused - press Ctrl+V to paste', 'info');
    }
  }

  showPasteError(message) {
    // Visual feedback for paste errors
    const pasteButtons = this.keyboard.querySelectorAll('[data-key="Paste"]');
    pasteButtons.forEach(btn => {
      const originalText = btn.textContent;

      // Show error state
      btn.textContent = 'Failed';
      btn.style.background = 'rgba(244, 67, 54, 0.4)';
      btn.style.borderColor = 'rgba(244, 67, 54, 0.6)';
      btn.style.color = 'white';

      // Reset after delay
      setTimeout(() => {
        btn.textContent = originalText;
        btn.style.background = 'rgba(255,255,255,0.1)';
        btn.style.borderColor = 'rgba(255,255,255,0.2)';
        btn.style.color = 'white';
      }, 2000);
    });

    console.log('📋 Paste error:', message);

    // Also show a brief tooltip or notification if available
    if (typeof showNotification === 'function') {
      showNotification(`Paste failed: ${message}`, 'error');
    }
  }

  toggleShift() {
    this.isShiftPressed = !this.isShiftPressed;
    console.log('Shift toggled:', this.isShiftPressed);

    // Update all shift key appearances
    const shiftKeys = this.keyboard.querySelectorAll('[data-key="Shift"]');
    shiftKeys.forEach(key => {
      if (this.isShiftPressed) {
        key.style.background = 'rgba(76, 175, 80, 0.3)';
        key.style.borderColor = 'rgba(76, 175, 80, 0.5)';
      } else {
        key.style.background = 'rgba(255,255,255,0.1)';
        key.style.borderColor = 'rgba(255,255,255,0.2)';
      }
    });
  }

  getShiftedChar(char) {
    const shiftMap = {
      '1': '!', '2': '@', '3': '#', '4': '$', '5': '%',
      '6': '^', '7': '&', '8': '*', '9': '(', '0': ')',
      '-': '_', '=': '+', '[': '{', ']': '}', ';': ':',
      "'": '"', ',': '<', '.': '>', '/': '?'
    };

    if (shiftMap[char]) {
      return shiftMap[char];
    } else if (char.length === 1 && char.match(/[a-z]/)) {
      return char.toUpperCase();
    }

    return char;
  }

  updateCurrentText() {
    if (!this.targetInput) {
      this.currentText = '';
      return;
    }

    try {
      if (this.isStandardInput()) {
        this.currentText = this.targetInput.value || '';
      } else if (this.isContentEditable()) {
        // Use textContent for better compatibility
        this.currentText = this.targetInput.textContent || this.targetInput.innerText || '';
      } else {
        this.currentText = '';
      }
    } catch (error) {
      console.warn('Error updating current text:', error);
      this.currentText = '';
    }
  }

  updateCursorPosition() {
    if (!this.targetInput) {
      this.cursorPosition = 0;
      return;
    }

    try {
      if (this.isStandardInput()) {
        this.cursorPosition = this.targetInput.selectionStart || 0;
      } else if (this.isContentEditable()) {
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          this.cursorPosition = this.getTextOffsetFromRange(range);
        } else {
          this.cursorPosition = 0;
        }
      }
    } catch (error) {
      console.warn('Error updating cursor position:', error);
      this.cursorPosition = 0;
    }
  }

  getTextOffsetFromRange(range) {
    try {
      const container = this.targetInput;
      const walker = document.createTreeWalker(
        container,
        NodeFilter.SHOW_TEXT,
        null,
        false
      );

      let offset = 0;
      let node;

      while (node = walker.nextNode()) {
        if (node === range.startContainer) {
          return offset + range.startOffset;
        }
        offset += node.textContent.length;
      }

      return offset;
    } catch (error) {
      console.warn('Error calculating text offset:', error);
      return 0;
    }
  }

  async updateSuggestions() {
    if (!this.targetInput || !this.currentText) {
      this.hideSuggestions();
      return;
    }

    // Get the current word being typed
    const cursorPos = this.getCursorPosition();
    const textBeforeCursor = this.currentText.substring(0, cursorPos);

    // Extract current word more accurately
    const currentWord = this.extractCurrentWord(textBeforeCursor);

    if (currentWord.length < 2) {
      this.hideSuggestions();
      return;
    }

    // Get suggestions from multiple sources
    let suggestions = [];

    // 1. Static word suggestions (fast, always available)
    const staticSuggestions = this.findBestSuggestions(currentWord);
    suggestions.push(...staticSuggestions);

    // 2. AI-powered suggestions (smart, context-aware)
    if (this.aiSuggestionsEnabled && currentWord.length >= 3) {
      try {
        const aiSuggestions = await this.getAISuggestions(currentWord, textBeforeCursor);
        suggestions = this.mergeSuggestions(staticSuggestions, aiSuggestions);
      } catch (error) {
        console.warn('AI suggestions failed, using static suggestions:', error);
      }
    }

    if (suggestions.length > 0) {
      this.showSuggestions(suggestions, currentWord);
    } else {
      this.hideSuggestions();
    }
  }

  extractCurrentWord(textBeforeCursor) {
    // More sophisticated word extraction
    const wordMatch = textBeforeCursor.match(/[a-zA-Z0-9_]+$/);
    return wordMatch ? wordMatch[0] : '';
  }

  findBestSuggestions(currentWord) {
    const lowerCurrentWord = currentWord.toLowerCase();
    const suggestions = [];

    // Exact prefix matches (highest priority)
    const exactMatches = this.commonWords.filter(word =>
      word.toLowerCase().startsWith(lowerCurrentWord) &&
      word.toLowerCase() !== lowerCurrentWord
    );

    // Fuzzy matches (contains the typed text)
    const fuzzyMatches = this.commonWords.filter(word =>
      !word.toLowerCase().startsWith(lowerCurrentWord) &&
      word.toLowerCase().includes(lowerCurrentWord) &&
      word.toLowerCase() !== lowerCurrentWord
    );

    // Combine and prioritize
    suggestions.push(...exactMatches.slice(0, 4));
    suggestions.push(...fuzzyMatches.slice(0, 2));

    // Sort by relevance (shorter words first for exact matches)
    return suggestions
      .sort((a, b) => {
        const aExact = a.toLowerCase().startsWith(lowerCurrentWord);
        const bExact = b.toLowerCase().startsWith(lowerCurrentWord);

        if (aExact && bExact) {
          return a.length - b.length; // Shorter first for exact matches
        } else if (aExact) {
          return -1; // Exact matches first
        } else if (bExact) {
          return 1;
        } else {
          return a.length - b.length; // Shorter first for fuzzy matches
        }
      })
      .slice(0, 6); // Limit to 6 suggestions
  }

  getCursorPosition() {
    return this.cursorPosition;
  }

  showSuggestions(suggestions, currentWord) {
    this.suggestionsContainer.innerHTML = '';
    this.suggestionsContainer.style.display = 'flex';

    suggestions.forEach(suggestion => {
      const suggestionBtn = document.createElement('button');
      suggestionBtn.textContent = suggestion;
      suggestionBtn.style.cssText = `
        background: rgba(76, 175, 80, 0.2);
        border: 1px solid rgba(76, 175, 80, 0.3);
        border-radius: 4px;
        color: white;
        padding: 4px 8px;
        margin: 2px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s ease;
        user-select: none;
      `;

      suggestionBtn.addEventListener('mouseenter', () => {
        suggestionBtn.style.background = 'rgba(76, 175, 80, 0.4)';
      });

      suggestionBtn.addEventListener('mouseleave', () => {
        suggestionBtn.style.background = 'rgba(76, 175, 80, 0.2)';
      });

      // Prevent focus on suggestion buttons
      suggestionBtn.addEventListener('mousedown', (e) => {
        e.preventDefault();
        e.stopPropagation();
      });

      suggestionBtn.addEventListener('focus', (e) => {
        e.preventDefault();
        e.target.blur();
      });

      suggestionBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.applySuggestion(suggestion, currentWord);
      });

      this.suggestionsContainer.appendChild(suggestionBtn);
    });
  }

  hideSuggestions() {
    this.suggestionsContainer.style.display = 'none';
  }

  applySuggestion(suggestion, currentWord) {
    if (!this.targetInput || !suggestion) return;

    try {
      if (this.isStandardInput()) {
        this.applySuggestionToStandardInput(suggestion, currentWord);
      } else if (this.isContentEditable()) {
        this.applySuggestionToContentEditable(suggestion, currentWord);
      }

      // Update internal state
      this.updateCursorPosition();
      this.updateCurrentText();
      this.hideSuggestions();

      // Trigger input event
      this.targetInput.dispatchEvent(new Event('input', { bubbles: true }));

    } catch (error) {
      console.warn('Error applying suggestion:', error);
    }
  }

  applySuggestionToStandardInput(suggestion, currentWord) {
    const element = this.targetInput;
    const cursorPos = element.selectionStart || 0;
    const value = element.value || '';

    // Find the start of the current word
    const textBeforeCursor = value.substring(0, cursorPos);
    const wordStartIndex = this.findWordStart(textBeforeCursor);

    // Replace the current word with the suggestion
    const beforeWord = value.substring(0, wordStartIndex);
    const afterCursor = value.substring(cursorPos);
    const newValue = beforeWord + suggestion + afterCursor;

    // Apply the change
    element.value = newValue;
    const newCursorPos = wordStartIndex + suggestion.length;
    element.setSelectionRange(newCursorPos, newCursorPos);

    // Ensure focus
    if (document.activeElement !== element) {
      element.focus();
    }
  }

  applySuggestionToContentEditable(suggestion, currentWord) {
    const selection = window.getSelection();

    if (selection.rangeCount === 0) {
      this.targetInput.focus();
      if (selection.rangeCount === 0) return;
    }

    const range = selection.getRangeAt(0);
    const cursorPos = this.getTextOffsetFromRange(range);
    const textBeforeCursor = this.currentText.substring(0, cursorPos);
    const wordStartIndex = this.findWordStart(textBeforeCursor);

    // Create range for the current word
    const wordRange = this.createRangeFromTextOffset(wordStartIndex, cursorPos);

    if (wordRange) {
      // Replace the word
      wordRange.deleteContents();
      const textNode = document.createTextNode(suggestion);
      wordRange.insertNode(textNode);

      // Position cursor after the suggestion
      const newRange = document.createRange();
      newRange.setStartAfter(textNode);
      newRange.setEndAfter(textNode);

      selection.removeAllRanges();
      selection.addRange(newRange);
    }
  }

  findWordStart(textBeforeCursor) {
    // Find the start of the current word
    const match = textBeforeCursor.match(/[a-zA-Z0-9_]*$/);
    if (match) {
      return textBeforeCursor.length - match[0].length;
    }
    return textBeforeCursor.length;
  }

  createRangeFromTextOffset(startOffset, endOffset) {
    try {
      const walker = document.createTreeWalker(
        this.targetInput,
        NodeFilter.SHOW_TEXT,
        null,
        false
      );

      let currentOffset = 0;
      let startNode = null;
      let startPos = 0;
      let endNode = null;
      let endPos = 0;
      let node;

      while (node = walker.nextNode()) {
        const nodeLength = node.textContent.length;

        if (!startNode && currentOffset + nodeLength >= startOffset) {
          startNode = node;
          startPos = startOffset - currentOffset;
        }

        if (!endNode && currentOffset + nodeLength >= endOffset) {
          endNode = node;
          endPos = endOffset - currentOffset;
          break;
        }

        currentOffset += nodeLength;
      }

      if (startNode && endNode) {
        const range = document.createRange();
        range.setStart(startNode, startPos);
        range.setEnd(endNode, endPos);
        return range;
      }
    } catch (error) {
      console.warn('Error creating range from text offset:', error);
    }

    return null;
  }

  // ============ AI-POWERED SUGGESTIONS ============

  async getAISuggestions(currentWord, context) {
    // Check cache first
    const cacheKey = `${currentWord}:${context.slice(-50)}`;
    if (this.aiCache.has(cacheKey)) {
      return this.aiCache.get(cacheKey);
    }

    // Rate limiting
    const now = Date.now();
    if (now - this.lastAIRequest < this.aiRequestDelay) {
      return [];
    }
    this.lastAIRequest = now;

    try {
      // Try multiple AI approaches
      const suggestions = await this.getMultiSourceAISuggestions(currentWord, context);

      // Cache the results
      this.aiCache.set(cacheKey, suggestions);

      // Clean cache if it gets too large
      if (this.aiCache.size > 100) {
        const firstKey = this.aiCache.keys().next().value;
        this.aiCache.delete(firstKey);
      }

      return suggestions;
    } catch (error) {
      console.warn('AI suggestions error:', error);
      return [];
    }
  }

  async getMultiSourceAISuggestions(currentWord, context) {
    const suggestions = [];

    // 1. Local AI using browser's built-in capabilities
    const localSuggestions = await this.getLocalAISuggestions(currentWord, context);
    suggestions.push(...localSuggestions);

    // 2. Context-aware programming suggestions
    const programmingSuggestions = this.getContextAwareProgrammingSuggestions(currentWord, context);
    suggestions.push(...programmingSuggestions);

    // 3. Pattern-based suggestions from user history
    const patternSuggestions = this.getPatternBasedSuggestions(currentWord, context);
    suggestions.push(...patternSuggestions);

    // 4. If available, use external AI API (OpenAI, etc.)
    if (window.electronAPI && window.electronAPI.getAISuggestions) {
      try {
        const externalSuggestions = await window.electronAPI.getAISuggestions(currentWord, context);
        suggestions.push(...externalSuggestions);
      } catch (error) {
        console.warn('External AI API failed:', error);
      }
    }

    // Remove duplicates and return top suggestions
    return [...new Set(suggestions)].slice(0, 6);
  }

  async getLocalAISuggestions(currentWord, context) {
    // Use browser's built-in text prediction capabilities
    const suggestions = [];

    try {
      // Analyze context for technical terms
      const contextWords = context.toLowerCase().split(/\s+/).slice(-10);
      const technicalContext = this.detectTechnicalContext(contextWords);

      // Generate suggestions based on context
      if (technicalContext.includes('algorithm')) {
        suggestions.push(...this.getAlgorithmSuggestions(currentWord));
      }

      if (technicalContext.includes('data') || technicalContext.includes('structure')) {
        suggestions.push(...this.getDataStructureSuggestions(currentWord));
      }

      if (technicalContext.includes('code') || technicalContext.includes('function')) {
        suggestions.push(...this.getCodingSuggestions(currentWord));
      }

      // Interview-specific suggestions
      if (technicalContext.includes('interview') || technicalContext.includes('question')) {
        suggestions.push(...this.getInterviewSuggestions(currentWord));
      }

    } catch (error) {
      console.warn('Local AI suggestions error:', error);
    }

    return suggestions.filter(s => s.toLowerCase().startsWith(currentWord.toLowerCase()));
  }

  detectTechnicalContext(contextWords) {
    const technicalKeywords = [
      'algorithm', 'data', 'structure', 'code', 'function', 'class', 'method',
      'interview', 'question', 'problem', 'solution', 'complexity', 'optimize',
      'array', 'tree', 'graph', 'sort', 'search', 'hash', 'stack', 'queue'
    ];

    return contextWords.filter(word => technicalKeywords.includes(word));
  }

  getAlgorithmSuggestions(currentWord) {
    const algorithmTerms = [
      'algorithm', 'algorithms', 'algorithmic', 'approach', 'analysis',
      'binary_search', 'breadth_first', 'depth_first', 'dynamic_programming',
      'greedy', 'backtracking', 'divide_conquer', 'memoization',
      'time_complexity', 'space_complexity', 'big_o', 'optimization'
    ];
    return algorithmTerms.filter(term => term.startsWith(currentWord.toLowerCase()));
  }

  getDataStructureSuggestions(currentWord) {
    const dataStructureTerms = [
      'array', 'arraylist', 'linked_list', 'doubly_linked', 'stack', 'queue',
      'priority_queue', 'heap', 'binary_tree', 'binary_search_tree', 'avl_tree',
      'red_black_tree', 'graph', 'adjacency_list', 'adjacency_matrix',
      'hash_table', 'hash_map', 'dictionary', 'set', 'trie', 'segment_tree'
    ];
    return dataStructureTerms.filter(term => term.startsWith(currentWord.toLowerCase()));
  }

  getCodingSuggestions(currentWord) {
    const codingTerms = [
      'function', 'method', 'class', 'object', 'variable', 'parameter',
      'return', 'loop', 'iteration', 'recursion', 'condition', 'boolean',
      'string', 'integer', 'array', 'list', 'dictionary', 'tuple',
      'exception', 'error', 'debug', 'test', 'unit_test', 'refactor'
    ];
    return codingTerms.filter(term => term.startsWith(currentWord.toLowerCase()));
  }

  getInterviewSuggestions(currentWord) {
    const interviewTerms = [
      'interview', 'question', 'answer', 'solution', 'approach', 'strategy',
      'explanation', 'example', 'edge_case', 'test_case', 'optimization',
      'trade_off', 'pros_cons', 'alternative', 'implementation', 'pseudocode',
      'walkthrough', 'demonstration', 'clarification', 'assumption'
    ];
    return interviewTerms.filter(term => term.startsWith(currentWord.toLowerCase()));
  }

  getContextAwareProgrammingSuggestions(currentWord, context) {
    const suggestions = [];
    const lowerContext = context.toLowerCase();

    // Language-specific suggestions
    if (lowerContext.includes('javascript') || lowerContext.includes('js')) {
      suggestions.push(...this.getJavaScriptSuggestions(currentWord));
    }

    if (lowerContext.includes('python') || lowerContext.includes('py')) {
      suggestions.push(...this.getPythonSuggestions(currentWord));
    }

    if (lowerContext.includes('java') && !lowerContext.includes('javascript')) {
      suggestions.push(...this.getJavaSuggestions(currentWord));
    }

    // Framework-specific suggestions
    if (lowerContext.includes('react')) {
      suggestions.push(...this.getReactSuggestions(currentWord));
    }

    return suggestions;
  }

  getJavaScriptSuggestions(currentWord) {
    const jsTerms = [
      'javascript', 'function', 'const', 'let', 'var', 'arrow_function',
      'async', 'await', 'promise', 'callback', 'closure', 'prototype',
      'this', 'bind', 'call', 'apply', 'map', 'filter', 'reduce',
      'forEach', 'find', 'includes', 'indexOf', 'slice', 'splice'
    ];
    return jsTerms.filter(term => term.startsWith(currentWord.toLowerCase()));
  }

  getPythonSuggestions(currentWord) {
    const pythonTerms = [
      'python', 'def', 'class', 'import', 'from', 'lambda', 'list_comprehension',
      'dictionary', 'tuple', 'set', 'range', 'enumerate', 'zip', 'map',
      'filter', 'reduce', 'generator', 'decorator', 'context_manager'
    ];
    return pythonTerms.filter(term => term.startsWith(currentWord.toLowerCase()));
  }

  getJavaSuggestions(currentWord) {
    const javaTerms = [
      'java', 'public', 'private', 'protected', 'static', 'final', 'abstract',
      'interface', 'extends', 'implements', 'override', 'super', 'this',
      'ArrayList', 'HashMap', 'HashSet', 'LinkedList', 'TreeMap', 'TreeSet'
    ];
    return javaTerms.filter(term => term.startsWith(currentWord.toLowerCase()));
  }

  getReactSuggestions(currentWord) {
    const reactTerms = [
      'react', 'component', 'useState', 'useEffect', 'useContext', 'useReducer',
      'props', 'state', 'jsx', 'render', 'lifecycle', 'hooks', 'virtual_dom'
    ];
    return reactTerms.filter(term => term.startsWith(currentWord.toLowerCase()));
  }

  getPatternBasedSuggestions(currentWord, context) {
    // Analyze user's typing patterns and suggest based on history
    const suggestions = [];

    // Update typing patterns
    this.updateTypingPatterns(currentWord, context);

    // Get suggestions based on patterns
    for (const [pattern, frequency] of this.userTypingPatterns) {
      if (pattern.startsWith(currentWord.toLowerCase()) && frequency > 1) {
        suggestions.push(pattern);
      }
    }

    return suggestions.sort((a, b) =>
      (this.userTypingPatterns.get(b) || 0) - (this.userTypingPatterns.get(a) || 0)
    ).slice(0, 3);
  }

  updateTypingPatterns(currentWord, context) {
    // Track user's typing patterns for personalized suggestions
    const words = context.toLowerCase().split(/\s+/).slice(-5);

    words.forEach(word => {
      if (word.length > 2) {
        const count = this.userTypingPatterns.get(word) || 0;
        this.userTypingPatterns.set(word, count + 1);
      }
    });

    // Keep only recent patterns (limit memory usage)
    if (this.userTypingPatterns.size > 200) {
      const entries = Array.from(this.userTypingPatterns.entries());
      entries.sort((a, b) => b[1] - a[1]);
      this.userTypingPatterns.clear();
      entries.slice(0, 100).forEach(([word, count]) => {
        this.userTypingPatterns.set(word, count);
      });
    }
  }

  mergeSuggestions(staticSuggestions, aiSuggestions) {
    // Intelligently merge static and AI suggestions
    const merged = [];
    const seen = new Set();

    // Add top AI suggestions first (they're more contextual)
    aiSuggestions.slice(0, 3).forEach(suggestion => {
      if (!seen.has(suggestion.toLowerCase())) {
        merged.push(suggestion);
        seen.add(suggestion.toLowerCase());
      }
    });

    // Fill remaining slots with static suggestions
    staticSuggestions.forEach(suggestion => {
      if (!seen.has(suggestion.toLowerCase()) && merged.length < 6) {
        merged.push(suggestion);
        seen.add(suggestion.toLowerCase());
      }
    });

    return merged;
  }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.onScreenKeyboard = new OnScreenKeyboard();
  });
} else {
  window.onScreenKeyboard = new OnScreenKeyboard();
}
