// InterviewCracker Bookmarklet Helper
// Simple, reliable solution that works on any website

(function() {
  'use strict';
  
  // Check if already loaded
  if (window.InterviewCrackerHelper) {
    window.InterviewCrackerHelper.toggle();
    return;
  }

  class InterviewCrackerHelper {
    constructor() {
      this.isVisible = false;
      this.container = null;
      this.chatInput = null;
      this.messagesContainer = null;
      this.init();
    }

    init() {
      console.log('🚀 InterviewCracker Helper initializing...');
      this.createUI();
      this.setupEventListeners();
      this.show();
    }

    createUI() {
      // Create main container
      this.container = document.createElement('div');
      this.container.id = 'interviewcracker-helper';
      this.container.innerHTML = `
        <div class="ic-header">
          <span>🤖 InterviewCracker Helper</span>
          <div class="ic-controls">
            <button class="ic-minimize" title="Minimize">−</button>
            <button class="ic-close" title="Close">×</button>
          </div>
        </div>
        <div class="ic-content">
          <div class="ic-messages" id="ic-messages">
            <div class="ic-message ic-system">
              <strong>🤖 InterviewCracker Helper Ready!</strong><br>
              Type your questions below and I'll help you with coding problems.
            </div>
          </div>
          <div class="ic-input-area">
            <textarea id="ic-chat-input" placeholder="Ask me anything about coding, algorithms, or system design..."></textarea>
            <button id="ic-send-btn">Send</button>
          </div>
        </div>
      `;

      // Add styles
      const styles = `
        #interviewcracker-helper {
          position: fixed !important;
          top: 20px !important;
          right: 20px !important;
          width: 350px !important;
          height: 500px !important;
          background: #ffffff !important;
          border: 2px solid #4CAF50 !important;
          border-radius: 12px !important;
          box-shadow: 0 8px 32px rgba(0,0,0,0.3) !important;
          z-index: 999999 !important;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
          font-size: 14px !important;
          display: flex !important;
          flex-direction: column !important;
          overflow: hidden !important;
          resize: both !important;
          min-width: 300px !important;
          min-height: 400px !important;
        }

        .ic-header {
          background: linear-gradient(135deg, #4CAF50, #45a049) !important;
          color: white !important;
          padding: 12px 16px !important;
          font-weight: bold !important;
          display: flex !important;
          justify-content: space-between !important;
          align-items: center !important;
          cursor: move !important;
          user-select: none !important;
        }

        .ic-controls {
          display: flex !important;
          gap: 8px !important;
        }

        .ic-controls button {
          background: rgba(255,255,255,0.2) !important;
          border: none !important;
          color: white !important;
          width: 24px !important;
          height: 24px !important;
          border-radius: 4px !important;
          cursor: pointer !important;
          font-size: 16px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          transition: background 0.2s !important;
        }

        .ic-controls button:hover {
          background: rgba(255,255,255,0.3) !important;
        }

        .ic-content {
          flex: 1 !important;
          display: flex !important;
          flex-direction: column !important;
          overflow: hidden !important;
        }

        .ic-messages {
          flex: 1 !important;
          padding: 16px !important;
          overflow-y: auto !important;
          background: #f8f9fa !important;
        }

        .ic-message {
          margin-bottom: 12px !important;
          padding: 8px 12px !important;
          border-radius: 8px !important;
          line-height: 1.4 !important;
        }

        .ic-message.ic-user {
          background: #e3f2fd !important;
          margin-left: 20px !important;
        }

        .ic-message.ic-assistant {
          background: #e8f5e8 !important;
          margin-right: 20px !important;
        }

        .ic-message.ic-system {
          background: #fff3e0 !important;
          border-left: 4px solid #ff9800 !important;
          font-size: 12px !important;
        }

        .ic-input-area {
          padding: 16px !important;
          border-top: 1px solid #e0e0e0 !important;
          display: flex !important;
          gap: 8px !important;
        }

        #ic-chat-input {
          flex: 1 !important;
          padding: 8px 12px !important;
          border: 1px solid #ddd !important;
          border-radius: 6px !important;
          resize: none !important;
          font-family: inherit !important;
          font-size: 14px !important;
          outline: none !important;
          min-height: 36px !important;
          max-height: 100px !important;
        }

        #ic-chat-input:focus {
          border-color: #4CAF50 !important;
          box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2) !important;
        }

        #ic-send-btn {
          background: #4CAF50 !important;
          color: white !important;
          border: none !important;
          padding: 8px 16px !important;
          border-radius: 6px !important;
          cursor: pointer !important;
          font-weight: bold !important;
          transition: background 0.2s !important;
        }

        #ic-send-btn:hover {
          background: #45a049 !important;
        }

        #ic-send-btn:disabled {
          background: #ccc !important;
          cursor: not-allowed !important;
        }
      `;

      // Inject styles
      const styleSheet = document.createElement('style');
      styleSheet.textContent = styles;
      document.head.appendChild(styleSheet);

      // Add to page
      document.body.appendChild(this.container);

      // Get references
      this.chatInput = document.getElementById('ic-chat-input');
      this.messagesContainer = document.getElementById('ic-messages');
      this.sendBtn = document.getElementById('ic-send-btn');
    }

    setupEventListeners() {
      // Make draggable
      this.makeDraggable();

      // Control buttons
      this.container.querySelector('.ic-minimize').addEventListener('click', () => this.minimize());
      this.container.querySelector('.ic-close').addEventListener('click', () => this.close());

      // Send message
      this.sendBtn.addEventListener('click', () => this.sendMessage());
      this.chatInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          this.sendMessage();
        }
      });

      // Global shortcuts
      document.addEventListener('keydown', (e) => {
        if (e.ctrlKey && e.shiftKey && e.key === 'I') {
          e.preventDefault();
          this.toggle();
        }
        if (e.key === 'Escape' && this.isVisible) {
          e.preventDefault();
          this.hide();
        }
      });
    }

    makeDraggable() {
      const header = this.container.querySelector('.ic-header');
      let isDragging = false;
      let startX, startY, startLeft, startTop;

      header.addEventListener('mousedown', (e) => {
        if (e.target.tagName === 'BUTTON') return;
        
        isDragging = true;
        startX = e.clientX;
        startY = e.clientY;
        
        const rect = this.container.getBoundingClientRect();
        startLeft = rect.left;
        startTop = rect.top;
        
        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
        e.preventDefault();
      });

      const onMouseMove = (e) => {
        if (!isDragging) return;
        
        const deltaX = e.clientX - startX;
        const deltaY = e.clientY - startY;
        
        this.container.style.left = (startLeft + deltaX) + 'px';
        this.container.style.top = (startTop + deltaY) + 'px';
        this.container.style.right = 'auto';
      };

      const onMouseUp = () => {
        isDragging = false;
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
      };
    }

    sendMessage() {
      const message = this.chatInput.value.trim();
      if (!message) return;

      // Add user message
      this.addMessage(message, 'user');
      this.chatInput.value = '';

      // Simulate AI response (replace with actual API call)
      this.sendBtn.disabled = true;
      this.sendBtn.textContent = 'Thinking...';

      setTimeout(() => {
        const response = this.generateResponse(message);
        this.addMessage(response, 'assistant');
        this.sendBtn.disabled = false;
        this.sendBtn.textContent = 'Send';
        this.chatInput.focus();
      }, 1000);
    }

    addMessage(text, type) {
      const messageDiv = document.createElement('div');
      messageDiv.className = `ic-message ic-${type}`;
      
      if (type === 'user') {
        messageDiv.innerHTML = `<strong>You:</strong> ${text}`;
      } else {
        messageDiv.innerHTML = `<strong>🤖 Assistant:</strong> ${text}`;
      }

      this.messagesContainer.appendChild(messageDiv);
      this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    generateResponse(message) {
      // Simple response generator (replace with actual AI integration)
      const responses = [
        "I can help you with that! Let me think about the best approach...",
        "That's a great question! Here's how I would approach it:",
        "For this type of problem, I recommend considering these key points:",
        "Let me break this down step by step for you:",
        "This is a common interview question. Here's the optimal solution:"
      ];
      
      return responses[Math.floor(Math.random() * responses.length)];
    }

    show() {
      this.container.style.display = 'flex';
      this.isVisible = true;
      this.chatInput.focus();
    }

    hide() {
      this.container.style.display = 'none';
      this.isVisible = false;
    }

    toggle() {
      if (this.isVisible) {
        this.hide();
      } else {
        this.show();
      }
    }

    minimize() {
      this.container.style.height = '40px';
      this.container.querySelector('.ic-content').style.display = 'none';
    }

    close() {
      this.container.remove();
      delete window.InterviewCrackerHelper;
    }
  }

  // Initialize
  window.InterviewCrackerHelper = new InterviewCrackerHelper();

})();
