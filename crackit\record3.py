import soundcard as sc
import numpy as np
import sys
import time
import signal
import threading
import struct
import wave
import librosa
import requests
import argparse
from queue import Queue

# =================== Argument Parsing ===================
def parse_arguments():
    parser = argparse.ArgumentParser(description='Audio recorder with streaming and transcription capabilities')
    parser.add_argument('--user-id', type=str, help='User ID for authentication', default='anonymous')
    parser.add_argument('--session-id', type=str, help='Session ID for this recording session', default=None)
    parser.add_argument('--api-key', type=str, help='API key for authentication', default=None)
    return parser.parse_args()

# Parse command line arguments
args = parse_arguments()

# =================== Audio Configuration ===================
SAMPLE_RATE = 16000  # Optimal sample rate for speech recognition
CHUNK_DURATION_SEC = 0.2  # 200ms chunks for low latency
CHUNK_SIZE = int(SAMPLE_RATE * CHUNK_DURATION_SEC)  
SILENCE_THRESHOLD = 0.02  # RMS threshold for voice activity detection

# =================== Express Server Configuration ===================
EXPRESS_SERVER_URL = 'https://server-394748284637.us-central1.run.app'  # Express server URL - updated for App Engine
SETUP_ENDPOINT = '/setup-stream'  # Endpoint to set up the stream
CHUNK_ENDPOINT = '/stream-chunk'  # Endpoint to send audio chunks
PYTHON_TRANSCRIBE_ENDPOINT = '/python-transcribe'  # Endpoint to start transcription
TRANSCRIBE_ENDPOINT = '/simple-transcribe'  # Endpoint for direct transcription

print(f"DEBUG - Using server URL: {EXPRESS_SERVER_URL}")
print(f"DEBUG - Full stream-chunk endpoint: {EXPRESS_SERVER_URL}{CHUNK_ENDPOINT}")
sys.stdout.flush()

# =================== Authentication Configuration ===================
USER_ID = args.user_id
SESSION_ID = args.session_id or f"session-{int(time.time())}"
API_KEY = args.api_key

print(f"DEBUG - Raw command-line arguments received: {sys.argv}")
print(f"DEBUG - Parsed arguments: user_id={args.user_id}, session_id={args.session_id}, api_key={'present' if API_KEY else 'not present'}")
print(f"DEBUG - Final credentials being used: USER_ID={USER_ID}, SESSION_ID={SESSION_ID}")
sys.stdout.flush()

# =================== Global Variables ===================
recording = True
should_exit = False
audio_queue = Queue()
stream_queue = Queue()
transcription_queue = Queue()
recorded_audio = []
transcriptions = []

# =================== Signal Handling ===================
def signal_handler(sig, frame):
    global should_exit
    should_exit = True
    print("Signal received, shutting down...")
    sys.stdout.flush()

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# =================== WAV Header Generation ===================
def generate_wav_header(sample_rate, num_channels, bits_per_sample):
    """Generate streaming-compatible WAV header"""
    byte_rate = sample_rate * num_channels * bits_per_sample // 8
    block_align = num_channels * bits_per_sample // 8
    header = struct.pack('<4sI4s', b'RIFF', 0xFFFFFFFF, b'WAVE')
    fmt_chunk = struct.pack('<4sIHHIIHH', b'fmt ', 16, 1, num_channels, 
                          sample_rate, byte_rate, block_align, bits_per_sample)
    data_chunk_header = struct.pack('<4sI', b'data', 0xFFFFFFFF)
    return header + fmt_chunk + data_chunk_header

WAV_HEADER = generate_wav_header(SAMPLE_RATE, 1, 16)

# =================== Audio Recording ===================
def record_audio():
    """Record speaker output with error recovery"""
    global should_exit
    while not should_exit:
        try:
            print("Initializing loopback recorder...")
            sys.stdout.flush()
            
            speaker = sc.default_speaker()
            recorder = sc.get_microphone(
                id=str(speaker.name),
                include_loopback=True
            ).recorder(samplerate=48000)  # Original hardware sample rate
            
            with recorder:
                print(f"Recording from: {speaker.name}")
                while not should_exit:
                    if recording:
                        chunk = recorder.record(numframes=int(48000 * CHUNK_DURATION_SEC))
                        mono_chunk = chunk[:, 0]
                        audio_queue.put(mono_chunk)
                    else:
                        time.sleep(0.01)
        except Exception as e:
            print(f"Recording error: {str(e)} - Reconnecting...")
            time.sleep(1)

# =================== Audio Processing ===================
def process_audio():
    """Process audio with resampling and silence detection"""
    audio_buffer = bytes()
    while not should_exit:
        if not audio_queue.empty():
            # Resample from 48kHz to 16kHz
            chunk = audio_queue.get()
            resampled = librosa.resample(chunk, orig_sr=48000, target_sr=SAMPLE_RATE)
            
            # Silence detection
            rms = np.sqrt(np.mean(resampled**2))
            if rms > SILENCE_THRESHOLD:
                # Convert to 16-bit PCM
                audio_int16 = np.int16(np.clip(resampled * 32767, -32768, 32767))
                pcm_bytes = audio_int16.tobytes()
                
                # Jitter buffer (collect 3 chunks = 600ms)
                audio_buffer += pcm_bytes
                if len(audio_buffer) >= 3 * len(pcm_bytes):
                    stream_queue.put(audio_buffer[:3 * len(pcm_bytes)])
                    transcription_queue.put(audio_buffer[:3 * len(pcm_bytes)])
                    recorded_audio.append(audio_buffer[:3 * len(pcm_bytes)])
                    audio_buffer = audio_buffer[3 * len(pcm_bytes):]
        else:
            time.sleep(0.001)

# =================== Save Audio ===================
def save_audio():
    if recorded_audio:
        filename = f"{USER_ID}_{SESSION_ID}_recorded_audio.wav"
        with wave.open(filename, 'wb') as wf:
            wf.setnchannels(1)
            wf.setsampwidth(2)
            wf.setframerate(SAMPLE_RATE)
            wf.writeframes(b''.join(recorded_audio))
        print(f"Audio saved to {filename}")
        return filename
    return None

# =================== Stream to Express Server ===================
def setup_stream():
    """Set up the audio stream with the Express server"""
    try:
        print(f"Setting up stream with server at {EXPRESS_SERVER_URL}")
        sys.stdout.flush()
        
        response = requests.post(f"{EXPRESS_SERVER_URL}{SETUP_ENDPOINT}", 
            json={
                "sampleRate": SAMPLE_RATE,
                "numChannels": 1,
                "bitsPerSample": 16,
                "userId": USER_ID,
                "sessionId": SESSION_ID,
                "apiKey": API_KEY
            },
            headers={
                'Content-Type': 'application/json',
                'X-User-ID': USER_ID,
                'X-Session-ID': SESSION_ID
            },
            timeout=(5, 10)  # Connect timeout, read timeout
        )
        
        if response.status_code == 200:
            data = response.json()
            
            # Save the original stream URL from the server
            stream_url = data.get("streamUrl", "")
            
            # Override with hardcoded URL to ensure consistency
            hardcoded_stream_url = f"{EXPRESS_SERVER_URL}/stream-audio?userId={USER_ID}&sessionId={SESSION_ID}&time={int(time.time())}"
            
            print(f"Stream setup successful: {data}")
            print(f"Original stream URL: {stream_url}")
            print(f"Using hardcoded stream URL: {hardcoded_stream_url}")
            sys.stdout.flush()
            
            # Start transcription service with hardcoded URL
            start_transcription_service(hardcoded_stream_url)
            return True
        else:
            print(f"Failed to setup stream: Status {response.status_code}")
            print(f"Response: {response.text}")
            sys.stdout.flush()
            return False
    except requests.RequestException as e:
        print(f"Network error setting up stream: {str(e)}")
        sys.stdout.flush()
        return False
    except Exception as e:
        print(f"Unexpected error setting up stream: {str(e)}")
        sys.stdout.flush()
        return False

def start_transcription_service(stream_url):
    """Start the transcription service to listen to the stream URL"""
    try:
        response = requests.post(f"{EXPRESS_SERVER_URL}{PYTHON_TRANSCRIBE_ENDPOINT}", json={
            "userId": USER_ID,
            "sessionId": SESSION_ID,
            "format": "wav",
            "sampleRate": SAMPLE_RATE,
            "streamUrl": stream_url
        }, headers={
            'Content-Type': 'application/json',
            'X-User-ID': USER_ID,
            'X-Session-ID': SESSION_ID
        })
        
        if response.status_code == 200:
            data = response.json()
            print(f"Transcription service started: {data}")
            sys.stdout.flush()
            return True
        else:
            print(f"Failed to start transcription service: {response.text}")
            sys.stdout.flush()
            return False
    except Exception as e:
        print(f"Error starting transcription service: {str(e)}")
        sys.stdout.flush()
        return False

def stream_audio():
    """Stream audio chunks to Express server for playback and transcription"""
    # First, set up the stream
    retry_count = 0
    max_retries = 5
    
    while retry_count < max_retries:
        if setup_stream():
            break
        retry_count += 1
        wait_time = min(2 ** retry_count, 30)
        print(f"Failed to set up stream (attempt {retry_count}/{max_retries}). Retrying in {wait_time} seconds...")
        sys.stdout.flush()
        time.sleep(wait_time)
    
    if retry_count >= max_retries:
        print("Failed to set up stream after maximum retries. Aborting.")
        sys.stdout.flush()
        return
        
    # Send the WAV header to initialize the stream
    retry_count = 0
    while retry_count < max_retries:
        try:
            print(f"Sending WAV header to Express server (attempt {retry_count + 1}/{max_retries})")
            print(f"DEBUG - Using User ID: {USER_ID}, Session ID: {SESSION_ID}")
            sys.stdout.flush()
            
            headers = {
                'Content-Type': 'audio/wav',
                'X-User-ID': USER_ID,
                'X-Session-ID': SESSION_ID
            }
            if API_KEY:
                headers['X-API-Key'] = API_KEY
                
            response = requests.post(
                f"{EXPRESS_SERVER_URL}{CHUNK_ENDPOINT}", 
                data=WAV_HEADER,
                headers=headers,
                timeout=(5, 15)  # 5s connect, 15s read timeout
            )
            
            print(f"DEBUG - WAV header sent with response: {response.status_code}")
            print(f"DEBUG - Response content: {response.text[:100]}...")
            sys.stdout.flush()
            
            if response.status_code == 200:
                # Success! Let's proceed
                try:
                    response_data = response.json()
                    if response_data.get('success', False):
                        print("WAV header accepted by server successfully")
                        sys.stdout.flush()
                        break
                    else:
                        print(f"Server reported success=false in response: {response_data}")
                        sys.stdout.flush()
                except Exception as e:
                    print(f"Error parsing response JSON: {str(e)}")
                    sys.stdout.flush()
                # Even if JSON parsing fails but we got a 200, consider it a success
                break
            elif response.status_code == 503:
                # Service Unavailable - wait and retry
                retry_count += 1
                wait_time = min(2 ** retry_count, 30)
                print(f"Server returned 503 Service Unavailable. Retrying in {wait_time} seconds...")
                sys.stdout.flush()
                time.sleep(wait_time)
            else:
                # Some other error - retry a few times
                retry_count += 1
                wait_time = min(2 ** retry_count, 30)
                print(f"Failed to send WAV header: {response.status_code} - {response.text}")
                print(f"Retrying in {wait_time} seconds...")
                sys.stdout.flush()
                time.sleep(wait_time)
        except Exception as e:
            retry_count += 1
            wait_time = min(2 ** retry_count, 30)
            print(f"Error sending WAV header: {str(e)}")
            print(f"Retrying in {wait_time} seconds...")
            sys.stdout.flush()
            time.sleep(wait_time)
    
    if retry_count >= max_retries:
        print("Failed to send WAV header after maximum retries. Aborting.")
        sys.stdout.flush()
        return
        
    print(f"Starting audio streaming to Express server at {EXPRESS_SERVER_URL}")
    sys.stdout.flush()
    
    # Track failed attempts to implement backoff strategy
    consecutive_failures = 0
    max_backoff_seconds = 30
    
    while not should_exit:
        try:
            if not stream_queue.empty():
                chunk = stream_queue.get()
                
                # Send the audio chunk to the Express server
                headers = {
                    'Content-Type': 'audio/wav',
                    'X-User-ID': USER_ID,
                    'X-Session-ID': SESSION_ID
                }
                if API_KEY:
                    headers['X-API-Key'] = API_KEY
                    
                response = requests.post(
                    f"{EXPRESS_SERVER_URL}{CHUNK_ENDPOINT}", 
                    data=chunk,
                    headers=headers,
                    timeout=(5, 10)  # Connection timeout, read timeout
                )
                
                if response.status_code == 200:
                    print(f"Chunk sent successfully: {len(chunk)} bytes")
                    sys.stdout.flush()
                    consecutive_failures = 0  # Reset failure counter on success
                else:
                    consecutive_failures += 1
                    backoff_time = min(2 ** consecutive_failures, max_backoff_seconds)
                    print(f"Failed to send chunk: {response.status_code} - {response.text[:100]}. Backing off for {backoff_time}s")
                    sys.stdout.flush()
                    time.sleep(backoff_time)
            else:
                time.sleep(0.01)
        except requests.RequestException as e:
            consecutive_failures += 1
            backoff_time = min(2 ** consecutive_failures, max_backoff_seconds)
            print(f"Error sending chunk to Express server: {str(e)}. Backing off for {backoff_time}s")
            sys.stdout.flush()
            time.sleep(backoff_time)  # Exponential backoff

# =================== Transcribe Audio ===================
async def send_for_transcription():
    """Send audio chunks for transcription with improved error handling"""
    # Send the WAV header first to let the server know what's coming
    try:
        print(f"Sending WAV header to transcription service")
        headers = {
            'Content-Type': 'audio/wav',
            'X-User-ID': USER_ID,
            'X-Session-ID': SESSION_ID
        }
        if API_KEY:
            headers['X-API-Key'] = API_KEY
            
        response = requests.post(
            f"{EXPRESS_SERVER_URL}{TRANSCRIBE_ENDPOINT}", 
            data=WAV_HEADER,
            headers=headers,
            timeout=(3, 10)  # 3s connect, 10s read - shorter timeout for header
        )
        if response.status_code != 200:
            print(f"Failed to send WAV header for transcription: {response.text}")
    except Exception as e:
        print(f"Error sending WAV header for transcription: {str(e)}")
        
    print(f"Starting transcription service")
    
    # Track consecutive failures for backoff
    consecutive_failures = 0
    max_backoff_seconds = 30
    
    while not should_exit:
        try:
            if not transcription_queue.empty():
                chunk = transcription_queue.get()
                
                if consecutive_failures > 5:
                    print(f"Too many consecutive transcription failures ({consecutive_failures}), pausing for 10 seconds")
                    time.sleep(10)
                    consecutive_failures = 0
                
                # Send the audio chunk for transcription
                headers = {
                    'Content-Type': 'audio/wav',
                    'X-User-ID': USER_ID,
                    'X-Session-ID': SESSION_ID
                }
                if API_KEY:
                    headers['X-API-Key'] = API_KEY
                    
                response = requests.post(
                    f"{EXPRESS_SERVER_URL}{TRANSCRIBE_ENDPOINT}", 
                    data=chunk,
                    headers=headers,
                    timeout=(5, 15)  # Longer timeout for chunk processing
                )
                
                if response.status_code == 200:
                    result = response.json()
                    consecutive_failures = 0  # Reset on success
                    
                    if result.get('transcription'):
                        transcription = result['transcription']
                        timestamp = result.get('timestamp', 'unknown')
                        
                        # Store transcription with timestamp
                        if transcription.strip():
                            print(f"[{timestamp}] Transcription: {transcription}")
                            transcriptions.append((timestamp, transcription))
                else:
                    consecutive_failures += 1
                    backoff_time = min(2 ** consecutive_failures, max_backoff_seconds)
                    print(f"Failed to transcribe chunk (status {response.status_code}): {response.text[:100]}... Backing off for {backoff_time}s")
                    time.sleep(backoff_time)
            else:
                time.sleep(0.01)
        except requests.exceptions.Timeout:
            consecutive_failures += 1
            backoff_time = min(2 ** consecutive_failures, max_backoff_seconds)
            print(f"Timeout error sending chunk for transcription. Backing off for {backoff_time}s")
            time.sleep(backoff_time)
        except requests.exceptions.ConnectionError:
            consecutive_failures += 1
            backoff_time = min(2 ** consecutive_failures, max_backoff_seconds)
            print(f"Connection error sending chunk for transcription. Backing off for {backoff_time}s")
            time.sleep(backoff_time)
        except Exception as e:
            consecutive_failures += 1
            backoff_time = min(2 ** consecutive_failures, max_backoff_seconds)
            print(f"Error sending chunk for transcription: {str(e)}. Backing off for {backoff_time}s")
            time.sleep(backoff_time)

# =================== Main ===================
if __name__ == "__main__":
    print(f"Starting audio recorder with streaming and transcription for user {USER_ID}, session {SESSION_ID}...")
    # Initialize and start the threads
    record_thread = threading.Thread(target=record_audio, daemon=True)
    process_thread = threading.Thread(target=process_audio, daemon=True)
    stream_thread = threading.Thread(target=stream_audio, daemon=True)
    transcribe_thread = threading.Thread(target=send_for_transcription, daemon=True)
    
    record_thread.start()
    process_thread.start()
    stream_thread.start()
    transcribe_thread.start()

    print("Open your browser to:")
    print(f"  - Audio stream: {EXPRESS_SERVER_URL}/stream-test?userId={USER_ID}&sessionId={SESSION_ID}")
    print(f"  - Transcription logs are saved on the server in: transcripts_{USER_ID}_{SESSION_ID}.log")

    try:
        while not should_exit:
            time.sleep(0.1)
    finally:
        should_exit = True
        record_thread.join(timeout=1)
        process_thread.join(timeout=1)
        stream_thread.join(timeout=1)
        transcribe_thread.join(timeout=1)
        
        # Save recordings
        save_audio()
        
        # Save transcriptions
        if transcriptions:
            filename = f"{USER_ID}_{SESSION_ID}_transcriptions.txt"
            with open(filename, "w") as f:
                for timestamp, text in transcriptions:
                    f.write(f"[{timestamp}] {text}\n")
            print(f"Transcriptions saved to {filename}")
        
        print("Exit complete")
