<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interview Platform - Smart Focus Detection</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .interview-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
        }

        .alert-banner {
            background: #ff6b6b;
            color: white;
            padding: 15px;
            text-align: center;
            display: none;
            animation: slideDown 0.3s ease;
        }

        .alert-banner.show {
            display: block;
        }

        .safe-banner {
            background: #4CAF50;
            color: white;
            padding: 10px;
            text-align: center;
            font-size: 14px;
        }

        @keyframes slideDown {
            from { transform: translateY(-100%); }
            to { transform: translateY(0); }
        }

        .content {
            padding: 30px;
        }

        .interview-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }

        .question {
            font-size: 1.2rem;
            margin-bottom: 20px;
            color: #333;
        }

        .code-editor {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            min-height: 200px;
            margin-bottom: 20px;
        }

        .status-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            min-width: 250px;
            z-index: 1000;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .status-value {
            font-weight: bold;
        }

        .status-value.safe {
            color: #4CAF50;
        }

        .status-value.warning {
            color: #ff6b6b;
        }

        .activity-log {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-item {
            font-size: 12px;
            padding: 5px;
            border-left: 3px solid #4CAF50;
            margin-bottom: 5px;
            background: white;
            border-radius: 3px;
        }

        .log-item.suspicious {
            border-left-color: #ff6b6b;
        }

        .log-item.ignored {
            border-left-color: #9e9e9e;
            opacity: 0.7;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #2196F3;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #4CAF50;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <div class="alert-banner" id="alertBanner">
        ⚠️ SUSPICIOUS ACTIVITY DETECTED: You may have switched tabs or applications during the interview!
    </div>

    <div class="safe-banner">
        🛡️ InterviewCracker Helper Integration Active - Helper app focus changes will be ignored
    </div>

    <div class="interview-container">
        <div class="header">
            <h1>📝 Technical Interview</h1>
            <p>Code Challenge - Focus Detection Enabled</p>
        </div>

        <div class="content">
            <div class="interview-section">
                <div class="question">
                    <strong>Question 1:</strong> Implement a function that finds the longest palindromic substring in a given string.
                </div>
                
                <div class="code-editor" contenteditable="true">
def longest_palindrome(s):
    # Write your solution here
    pass

# Test cases
print(longest_palindrome("babad"))  # Expected: "bab" or "aba"
print(longest_palindrome("cbbd"))   # Expected: "bb"
                </div>
                
                <div style="display: flex; gap: 15px;">
                    <button class="btn btn-primary">Run Code</button>
                    <button class="btn btn-secondary">Submit Solution</button>
                </div>
            </div>

            <div class="interview-section">
                <div class="question">
                    <strong>Question 2:</strong> Design a data structure that supports insert, delete, and getRandom operations in O(1) time.
                </div>
                
                <div class="code-editor" contenteditable="true">
class RandomizedSet:
    def __init__(self):
        # Initialize your data structure here
        pass
    
    def insert(self, val):
        # Insert a value to the set
        pass
    
    def remove(self, val):
        # Remove a value from the set
        pass
    
    def getRandom(self):
        # Get a random element from the set
        pass
                </div>
            </div>
        </div>
    </div>

    <!-- Status Panel -->
    <div class="status-panel">
        <h3 style="margin-bottom: 15px; color: #333;">Focus Monitor</h3>
        
        <div class="status-item">
            <span>Interview Mode:</span>
            <label class="toggle-switch">
                <input type="checkbox" id="interviewModeToggle" checked>
                <span class="slider"></span>
            </label>
        </div>

        <div class="status-item">
            <span>Helper Active:</span>
            <span class="status-value safe" id="helperStatus">Detecting...</span>
        </div>

        <div class="status-item">
            <span>Suspicious Events:</span>
            <span class="status-value" id="suspiciousCount">0</span>
        </div>

        <div class="status-item">
            <span>Ignored Events:</span>
            <span class="status-value" id="ignoredCount">0</span>
        </div>

        <div class="activity-log" id="activityLog">
            <div class="log-item">Focus monitor initialized</div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="testFocusDetection()">Test</button>
            <button class="btn btn-secondary" onclick="clearLog()">Clear</button>
        </div>
    </div>

    <!-- Include the focus integration script -->
    <script src="focus-integration.js"></script>
    
    <script>
        // Initialize the focus integration
        const focusIntegration = new InterviewCrackerFocusIntegration({
            interviewMode: true,
            gracePeriod: 2000,
            enableLogging: true,
            onSuspiciousActivity: handleSuspiciousActivity,
            onHelperDetected: handleHelperDetected
        });

        // UI elements
        const alertBanner = document.getElementById('alertBanner');
        const helperStatusEl = document.getElementById('helperStatus');
        const suspiciousCountEl = document.getElementById('suspiciousCount');
        const ignoredCountEl = document.getElementById('ignoredCount');
        const activityLogEl = document.getElementById('activityLog');
        const interviewModeToggle = document.getElementById('interviewModeToggle');

        // Handle suspicious activity
        function handleSuspiciousActivity(event) {
            // Show alert banner
            showAlert();
            
            // Update stats
            updateStats();
            
            // Add to log
            addLogItem(event.description, 'suspicious');
            
            // You could also send this to your server for monitoring
            // sendToServer('suspicious_activity', event);
        }

        // Handle helper detection
        function handleHelperDetected(isActive) {
            helperStatusEl.textContent = isActive ? 'Active' : 'Inactive';
            helperStatusEl.className = `status-value ${isActive ? 'safe' : 'warning'}`;
            
            if (isActive) {
                addLogItem('InterviewCracker helper detected', 'ignored');
            }
        }

        // Show alert banner
        function showAlert() {
            alertBanner.classList.add('show');
            setTimeout(() => {
                alertBanner.classList.remove('show');
            }, 5000);
        }

        // Update statistics
        function updateStats() {
            const stats = focusIntegration.getStats();
            suspiciousCountEl.textContent = stats.suspiciousCount;
            ignoredCountEl.textContent = stats.ignoredCount;
            
            // Update color based on suspicious count
            if (stats.suspiciousCount > 0) {
                suspiciousCountEl.className = 'status-value warning';
            } else {
                suspiciousCountEl.className = 'status-value safe';
            }
        }

        // Add item to activity log
        function addLogItem(message, type) {
            const logItem = document.createElement('div');
            logItem.className = `log-item ${type}`;
            logItem.innerHTML = `
                <div style="font-weight: bold;">${new Date().toLocaleTimeString()}</div>
                <div>${message}</div>
            `;
            
            activityLogEl.insertBefore(logItem, activityLogEl.firstChild);
            
            // Limit log items
            while (activityLogEl.children.length > 20) {
                activityLogEl.removeChild(activityLogEl.lastChild);
            }
        }

        // Interview mode toggle
        interviewModeToggle.addEventListener('change', (e) => {
            focusIntegration.setInterviewMode(e.target.checked);
            addLogItem(`Interview mode ${e.target.checked ? 'enabled' : 'disabled'}`, 'info');
        });

        // Test function
        function testFocusDetection() {
            addLogItem('Focus detection test triggered', 'info');
            
            // Simulate suspicious activity for demo
            setTimeout(() => {
                if (!focusIntegration.shouldIgnoreEvent('test')) {
                    handleSuspiciousActivity({
                        type: 'test',
                        description: 'Test suspicious activity',
                        timestamp: new Date().toISOString()
                    });
                }
            }, 1000);
        }

        // Clear log function
        function clearLog() {
            focusIntegration.clearEvents();
            activityLogEl.innerHTML = '<div class="log-item">Log cleared</div>';
            updateStats();
        }

        // Enhanced communication with InterviewCracker app
        function setupInterviewCrackerCommunication() {
            // Listen for messages from InterviewCracker app
            window.addEventListener('message', (event) => {
                if (event.data && event.data.source === 'interviewcracker') {
                    switch (event.data.type) {
                        case 'helper_status':
                            handleHelperDetected(event.data.active);
                            break;
                        case 'screenshot_taken':
                            addLogItem('Screenshot captured by helper', 'ignored');
                            break;
                        case 'ai_analysis':
                            addLogItem('AI analysis in progress', 'ignored');
                            break;
                    }
                }
            });

            // Try to establish communication with helper app
            if (window.electronAPI) {
                addLogItem('Connected to InterviewCracker app via Electron API', 'info');
                helperStatusEl.textContent = 'Connected';
                helperStatusEl.className = 'status-value safe';
            }
        }

        // Initialize communication
        setupInterviewCrackerCommunication();

        // Send periodic heartbeat to detect helper app
        setInterval(() => {
            // Try to communicate with helper app
            try {
                if (window.electronAPI) {
                    // Post message to indicate we're active
                    window.postMessage({
                        type: 'interviewcracker-webpage',
                        status: 'active',
                        timestamp: Date.now()
                    }, '*');
                }
            } catch (error) {
                // Ignore communication errors
            }
        }, 5000);

        // Update stats periodically
        setInterval(updateStats, 1000);
    </script>
</body>
</html>
