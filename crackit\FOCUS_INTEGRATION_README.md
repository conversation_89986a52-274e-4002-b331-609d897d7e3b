# InterviewCracker Smart Focus Detection Integration

This integration allows your interview website to intelligently detect when users switch tabs or applications, while ignoring legitimate InterviewCracker helper app usage.

## 🚀 Quick Integration

### Method 1: Simple Integration (Recommended)

Add this to your interview webpage:

```html
<!-- Include the focus integration script -->
<script src="focus-integration.js"></script>

<script>
// Initialize smart focus detection
const focusIntegration = new InterviewCrackerFocusIntegration({
    interviewMode: true,
    onSuspiciousActivity: (event) => {
        console.log('SUSPICIOUS ACTIVITY:', event.description);
        // Handle suspicious activity (show warning, log to server, etc.)
        showWarningMessage(event.description);
    },
    onHelperDetected: (isActive) => {
        console.log('InterviewCracker Helper:', isActive ? 'Active' : 'Inactive');
        // Update UI to show helper status
    }
});

function showWarningMessage(message) {
    // Your warning logic here
    alert('⚠️ ' + message);
}
</script>
```

### Method 2: Full Integration Example

See `interview-integration-demo.html` for a complete example with:
- Real-time status monitoring
- Activity logging
- Customizable settings
- Visual alerts

## 🔧 Configuration Options

```javascript
const focusIntegration = new InterviewCrackerFocusIntegration({
    interviewMode: true,        // Ignore focus changes when helper is active
    gracePeriod: 2000,         // Ignore rapid focus changes (ms)
    enableLogging: true,       // Console logging
    onSuspiciousActivity: null, // Callback for suspicious events
    onHelperDetected: null     // Callback for helper status changes
});
```

## 📊 API Methods

```javascript
// Get statistics
const stats = focusIntegration.getStats();
console.log(stats); // { suspiciousCount, ignoredCount, totalEvents, helperActive }

// Get all events
const events = focusIntegration.getEvents();

// Toggle interview mode
focusIntegration.setInterviewMode(true/false);

// Clear event history
focusIntegration.clearEvents();
```

## 🧠 How It Works

### Smart Detection Logic

1. **InterviewCracker Helper Detection**:
   - Detects Electron API presence
   - Monitors rapid focus changes (< 500ms)
   - Checks for window visible but not focused state

2. **Interview Mode**:
   - When enabled, ignores focus events if helper app is detected
   - Maintains grace period for legitimate app switching
   - Only flags genuine suspicious behavior

3. **Event Classification**:
   - **Suspicious**: Genuine tab/app switching during interview
   - **Ignored**: Focus changes while helper app is active
   - **Grace Period**: Rapid focus changes that are likely legitimate

### Communication Protocol

The system uses `postMessage` API to communicate between your website and the InterviewCracker app:

```javascript
// Helper app sends status updates
window.postMessage({
    type: 'interviewcracker-helper',
    active: true/false,
    source: 'interviewcracker',
    timestamp: Date.now()
}, '*');

// Helper app notifies of actions
window.postMessage({
    type: 'interviewcracker-action',
    action: 'screenshot_taken',
    data: { timestamp: Date.now() },
    source: 'interviewcracker'
}, '*');
```

## 🎯 Use Cases

### 1. Interview Monitoring
Perfect for coding interviews where you want to detect cheating but allow legitimate helper app usage.

### 2. Exam Proctoring
Monitor student focus while allowing approved tools and applications.

### 3. Training Platforms
Track engagement while permitting educational support tools.

## ⚙️ Customization

### Custom Warning Messages
```javascript
const focusIntegration = new InterviewCrackerFocusIntegration({
    onSuspiciousActivity: (event) => {
        switch(event.type) {
            case 'tab_hidden':
                showCustomWarning('Please stay on the interview tab');
                break;
            case 'window_blur':
                showCustomWarning('Please don\'t switch to other applications');
                break;
        }
    }
});
```

### Server Logging
```javascript
const focusIntegration = new InterviewCrackerFocusIntegration({
    onSuspiciousActivity: (event) => {
        // Log to your server
        fetch('/api/log-suspicious-activity', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                userId: getCurrentUserId(),
                event: event,
                timestamp: new Date().toISOString()
            })
        });
    }
});
```

## 🔒 Security Considerations

1. **Client-Side Only**: This is a client-side detection system for user experience, not security enforcement
2. **Server Validation**: Always validate important actions on the server side
3. **User Privacy**: Be transparent about monitoring and get user consent
4. **Graceful Degradation**: The system works even if InterviewCracker is not present

## 📱 Browser Compatibility

- ✅ Chrome 63+
- ✅ Firefox 62+
- ✅ Safari 13+
- ✅ Edge 79+

Uses standard APIs:
- Page Visibility API
- Window focus/blur events
- PostMessage API

## 🛠️ Troubleshooting

### Helper App Not Detected
1. Ensure InterviewCracker app is running
2. Check browser console for communication errors
3. Verify the integration script is loaded

### False Positives
1. Increase grace period: `gracePeriod: 3000`
2. Add applications to whitelist
3. Disable strict mode

### No Events Logged
1. Check if `enableLogging: true`
2. Verify callback functions are set
3. Test with manual focus changes

## 🔗 Files Included

- `focus-integration.js` - Main integration script
- `interview-smart-detector.html` - Standalone detector with full UI
- `interview-integration-demo.html` - Integration example
- `README.md` - This documentation

## 📞 Support

For issues or questions about the InterviewCracker integration, please check the console logs and ensure all files are properly included in your project.
