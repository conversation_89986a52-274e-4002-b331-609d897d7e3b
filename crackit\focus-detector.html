<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Focus & Tab Change Detector</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .status-section {
            padding: 30px;
        }

        .status-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 5px solid #4CAF50;
            transition: all 0.3s ease;
        }

        .status-card.inactive {
            border-left-color: #ff6b6b;
            background: #fff5f5;
        }

        .status-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .status-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .status-value.active {
            color: #4CAF50;
        }

        .status-value.inactive {
            color: #ff6b6b;
        }

        .timestamp {
            font-size: 0.9rem;
            color: #666;
            font-style: italic;
        }

        .events-section {
            padding: 0 30px 30px;
        }

        .events-title {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        .event-log {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
        }

        .event-item {
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
            background: white;
            transition: all 0.3s ease;
        }

        .event-item.focus-lost {
            border-left-color: #ff6b6b;
        }

        .event-item.tab-hidden {
            border-left-color: #ffa726;
        }

        .event-time {
            font-weight: bold;
            color: #555;
        }

        .event-description {
            margin-top: 5px;
            color: #666;
        }

        .controls {
            padding: 20px 30px;
            border-top: 1px solid #eee;
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #4CAF50;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        @media (max-width: 600px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .controls {
                flex-direction: column;
            }
            
            .stats {
                grid-template-columns: 1fr;
            }
        }

        /* Animation for when tab becomes inactive */
        body.tab-inactive {
            filter: grayscale(0.3);
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Focus & Tab Change Detector</h1>
            <p>Real-time monitoring of window focus, tab visibility, and user activity</p>
        </div>

        <div class="status-section">
            <div class="status-card" id="tabStatus">
                <div class="status-title">Tab Visibility</div>
                <div class="status-value active" id="tabStatusValue">Visible & Active</div>
                <div class="timestamp" id="tabStatusTime">Last updated: --</div>
            </div>

            <div class="status-card" id="windowStatus">
                <div class="status-title">Window Focus</div>
                <div class="status-value active" id="windowStatusValue">Focused</div>
                <div class="timestamp" id="windowStatusTime">Last updated: --</div>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="focusLostCount">0</div>
                    <div class="stat-label">Focus Lost Events</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="tabHiddenCount">0</div>
                    <div class="stat-label">Tab Hidden Events</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalEvents">0</div>
                    <div class="stat-label">Total Events</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="sessionTime">00:00</div>
                    <div class="stat-label">Session Time</div>
                </div>
            </div>
        </div>

        <div class="events-section">
            <div class="events-title">📋 Event Log</div>
            <div class="event-log" id="eventLog">
                <div class="event-item">
                    <div class="event-time">Session Started</div>
                    <div class="event-description">Focus detector initialized and ready to monitor events</div>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="testDetection()">🧪 Test Detection</button>
            <button class="btn btn-secondary" onclick="clearLog()">🗑️ Clear Log</button>
            <button class="btn btn-secondary" onclick="downloadLog()">💾 Download Log</button>
        </div>
    </div>

    <script>
        class FocusDetector {
            constructor() {
                this.events = [];
                this.focusLostCount = 0;
                this.tabHiddenCount = 0;
                this.sessionStartTime = Date.now();
                this.originalTitle = document.title;
                
                this.init();
                this.startSessionTimer();
            }

            init() {
                // Page Visibility API - detects tab changes
                document.addEventListener('visibilitychange', () => {
                    this.handleVisibilityChange();
                });

                // Window focus/blur events - detects window switching
                window.addEventListener('focus', () => {
                    this.handleWindowFocus();
                });

                window.addEventListener('blur', () => {
                    this.handleWindowBlur();
                });

                // Mouse leave/enter events - additional detection
                document.addEventListener('mouseleave', () => {
                    this.logEvent('Mouse left document area', 'mouse-leave');
                });

                document.addEventListener('mouseenter', () => {
                    this.logEvent('Mouse entered document area', 'mouse-enter');
                });

                // Keyboard events for additional focus detection
                document.addEventListener('keydown', (e) => {
                    if (e.altKey && e.key === 'Tab') {
                        this.logEvent('Alt+Tab detected (attempting to switch)', 'alt-tab');
                    }
                });

                // Beforeunload event - detects when user tries to leave
                window.addEventListener('beforeunload', () => {
                    this.logEvent('User attempting to leave page', 'before-unload');
                });

                console.log('Focus Detector initialized');
            }

            handleVisibilityChange() {
                const isHidden = document.hidden;
                const timestamp = new Date().toLocaleTimeString();
                
                if (isHidden) {
                    // Tab became hidden
                    this.tabHiddenCount++;
                    this.updateTabStatus('Hidden/Background', 'inactive', timestamp);
                    this.logEvent('Tab switched to background or minimized', 'tab-hidden');
                    document.title = '👋 Come back soon! - Focus Detector';
                    document.body.classList.add('tab-inactive');
                } else {
                    // Tab became visible
                    this.updateTabStatus('Visible & Active', 'active', timestamp);
                    this.logEvent('Tab switched to foreground', 'tab-visible');
                    document.title = this.originalTitle;
                    document.body.classList.remove('tab-inactive');
                }
                
                this.updateStats();
            }

            handleWindowFocus() {
                const timestamp = new Date().toLocaleTimeString();
                this.updateWindowStatus('Focused', 'active', timestamp);
                this.logEvent('Window gained focus', 'window-focus');
                this.updateStats();
            }

            handleWindowBlur() {
                const timestamp = new Date().toLocaleTimeString();
                this.focusLostCount++;
                this.updateWindowStatus('Not Focused', 'inactive', timestamp);
                this.logEvent('Window lost focus (switched to another app)', 'focus-lost');
                this.updateStats();
            }

            updateTabStatus(status, type, timestamp) {
                const statusElement = document.getElementById('tabStatusValue');
                const timeElement = document.getElementById('tabStatusTime');
                const cardElement = document.getElementById('tabStatus');
                
                statusElement.textContent = status;
                statusElement.className = `status-value ${type}`;
                timeElement.textContent = `Last updated: ${timestamp}`;
                
                if (type === 'inactive') {
                    cardElement.classList.add('inactive');
                } else {
                    cardElement.classList.remove('inactive');
                }
            }

            updateWindowStatus(status, type, timestamp) {
                const statusElement = document.getElementById('windowStatusValue');
                const timeElement = document.getElementById('windowStatusTime');
                const cardElement = document.getElementById('windowStatus');
                
                statusElement.textContent = status;
                statusElement.className = `status-value ${type}`;
                timeElement.textContent = `Last updated: ${timestamp}`;
                
                if (type === 'inactive') {
                    cardElement.classList.add('inactive');
                } else {
                    cardElement.classList.remove('inactive');
                }
            }

            logEvent(description, type) {
                const timestamp = new Date().toLocaleTimeString();
                const event = {
                    time: timestamp,
                    description: description,
                    type: type,
                    fullTimestamp: new Date().toISOString()
                };
                
                this.events.push(event);
                this.addEventToDOM(event);
                this.updateStats();
            }

            addEventToDOM(event) {
                const eventLog = document.getElementById('eventLog');
                const eventItem = document.createElement('div');
                eventItem.className = `event-item ${event.type}`;
                
                eventItem.innerHTML = `
                    <div class="event-time">${event.time}</div>
                    <div class="event-description">${event.description}</div>
                `;
                
                eventLog.insertBefore(eventItem, eventLog.firstChild);
                
                // Limit to 50 events in the DOM
                while (eventLog.children.length > 50) {
                    eventLog.removeChild(eventLog.lastChild);
                }
                
                // Scroll to top to show latest event
                eventLog.scrollTop = 0;
            }

            updateStats() {
                document.getElementById('focusLostCount').textContent = this.focusLostCount;
                document.getElementById('tabHiddenCount').textContent = this.tabHiddenCount;
                document.getElementById('totalEvents').textContent = this.events.length;
            }

            startSessionTimer() {
                setInterval(() => {
                    const elapsed = Date.now() - this.sessionStartTime;
                    const minutes = Math.floor(elapsed / 60000);
                    const seconds = Math.floor((elapsed % 60000) / 1000);
                    document.getElementById('sessionTime').textContent = 
                        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }, 1000);
            }

            clearLog() {
                this.events = [];
                this.focusLostCount = 0;
                this.tabHiddenCount = 0;
                const eventLog = document.getElementById('eventLog');
                eventLog.innerHTML = `
                    <div class="event-item">
                        <div class="event-time">Log Cleared</div>
                        <div class="event-description">Event log has been cleared</div>
                    </div>
                `;
                this.updateStats();
            }

            downloadLog() {
                const logData = {
                    sessionStartTime: new Date(this.sessionStartTime).toISOString(),
                    events: this.events,
                    stats: {
                        focusLostCount: this.focusLostCount,
                        tabHiddenCount: this.tabHiddenCount,
                        totalEvents: this.events.length
                    }
                };
                
                const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `focus-detection-log-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                this.logEvent('Focus detection log downloaded', 'download');
            }

            testDetection() {
                this.logEvent('Test event triggered manually', 'test');
                
                // Simulate a brief focus loss animation
                const container = document.querySelector('.container');
                container.classList.add('pulse');
                setTimeout(() => {
                    container.classList.remove('pulse');
                }, 2000);
            }
        }

        // Global functions for buttons
        function clearLog() {
            focusDetector.clearLog();
        }

        function downloadLog() {
            focusDetector.downloadLog();
        }

        function testDetection() {
            focusDetector.testDetection();
        }

        // Initialize the focus detector when the page loads
        let focusDetector;
        document.addEventListener('DOMContentLoaded', () => {
            focusDetector = new FocusDetector();
        });

        // Browser compatibility check
        if (typeof document.hidden === "undefined") {
            console.warn("Page Visibility API is not supported in this browser");
            document.body.innerHTML += `
                <div style="background: #ff6b6b; color: white; padding: 10px; text-align: center;">
                    ⚠️ Page Visibility API is not supported in this browser. Some features may not work.
                </div>
            `;
        }
    </script>
</body>
</html>
