<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interview Smart Focus Detector</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .settings-section {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .settings-title {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: #333;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .setting-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .setting-label {
            font-weight: 500;
            margin-bottom: 10px;
            color: #333;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #4CAF50;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .whitelist-section {
            padding: 20px;
        }

        .whitelist-input {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .whitelist-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }

        .whitelist-tag {
            background: #4CAF50;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .remove-tag {
            cursor: pointer;
            font-weight: bold;
        }

        .status-section {
            padding: 30px;
        }

        .status-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 5px solid #4CAF50;
            transition: all 0.3s ease;
        }

        .status-card.inactive {
            border-left-color: #ff6b6b;
            background: #fff5f5;
        }

        .status-card.interview-mode {
            border-left-color: #2196F3;
            background: #e3f2fd;
        }

        .status-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .status-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .status-value.active {
            color: #4CAF50;
        }

        .status-value.inactive {
            color: #ff6b6b;
        }

        .status-value.interview-mode {
            color: #2196F3;
        }

        .timestamp {
            font-size: 0.9rem;
            color: #666;
            font-style: italic;
        }

        .events-section {
            padding: 0 30px 30px;
        }

        .events-title {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        .event-log {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
        }

        .event-item {
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
            background: white;
            transition: all 0.3s ease;
        }

        .event-item.focus-lost {
            border-left-color: #ff6b6b;
        }

        .event-item.tab-hidden {
            border-left-color: #ffa726;
        }

        .event-item.whitelist-ignored {
            border-left-color: #9e9e9e;
            opacity: 0.7;
        }

        .event-item.interview-helper {
            border-left-color: #2196F3;
        }

        .event-time {
            font-weight: bold;
            color: #555;
        }

        .event-description {
            margin-top: 5px;
            color: #666;
        }

        .controls {
            padding: 20px 30px;
            border-top: 1px solid #eee;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .btn-interview {
            background: #2196F3;
            color: white;
        }

        .btn-interview:hover {
            background: #1976D2;
            transform: translateY(-2px);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #4CAF50;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        /* Animation for when tab becomes inactive */
        body.tab-inactive {
            filter: grayscale(0.3);
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .interview-mode-banner {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 15px 30px;
            text-align: center;
            font-weight: 500;
            display: none;
        }

        .interview-mode-banner.active {
            display: block;
        }

        @media (max-width: 600px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .controls {
                flex-direction: column;
            }
            
            .stats {
                grid-template-columns: 1fr;
            }

            .settings-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="interview-mode-banner" id="interviewBanner">
        🎯 Interview Mode Active - Helper app focus changes are ignored
    </div>

    <div class="container">
        <div class="header">
            <h1>🧠 Interview Smart Focus Detector</h1>
            <p>Intelligent monitoring with Interview Helper app integration</p>
        </div>

        <div class="settings-section">
            <div class="settings-title">⚙️ Detection Settings</div>
            <div class="settings-grid">
                <div class="setting-card">
                    <div class="setting-label">Interview Mode</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="interviewMode" checked>
                        <span class="slider"></span>
                    </label>
                    <p style="font-size: 0.9rem; color: #666; margin-top: 10px;">
                        Ignores focus changes when InterviewCracker helper is active
                    </p>
                </div>

                <div class="setting-card">
                    <div class="setting-label">Strict Mode</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="strictMode">
                        <span class="slider"></span>
                    </label>
                    <p style="font-size: 0.9rem; color: #666; margin-top: 10px;">
                        Detects all focus changes including mouse movements
                    </p>
                </div>

                <div class="setting-card">
                    <div class="setting-label">Audio Alerts</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="audioAlerts">
                        <span class="slider"></span>
                    </label>
                    <p style="font-size: 0.9rem; color: #666; margin-top: 10px;">
                        Play sound when suspicious activity is detected
                    </p>
                </div>
            </div>

            <div class="setting-card" style="margin-top: 20px;">
                <div class="setting-label">Whitelisted Applications</div>
                <div class="whitelist-section">
                    <input type="text" class="whitelist-input" id="whitelistInput" 
                           placeholder="Enter app name (e.g., InterviewCracker, VS Code, Notepad)">
                    <button class="btn btn-primary" onclick="addToWhitelist()">Add</button>
                    <div class="whitelist-tags" id="whitelistTags"></div>
                </div>
            </div>
        </div>

        <div class="status-section">
            <div class="status-card" id="tabStatus">
                <div class="status-title">Tab Visibility</div>
                <div class="status-value active" id="tabStatusValue">Visible & Active</div>
                <div class="timestamp" id="tabStatusTime">Last updated: --</div>
            </div>

            <div class="status-card" id="windowStatus">
                <div class="status-title">Window Focus</div>
                <div class="status-value active" id="windowStatusValue">Focused</div>
                <div class="timestamp" id="windowStatusTime">Last updated: --</div>
            </div>

            <div class="status-card interview-mode" id="interviewStatus">
                <div class="status-title">Interview Helper Status</div>
                <div class="status-value interview-mode" id="interviewStatusValue">Monitoring...</div>
                <div class="timestamp" id="interviewStatusTime">Waiting for helper connection</div>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="suspiciousCount">0</div>
                    <div class="stat-label">Suspicious Events</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="ignoredCount">0</div>
                    <div class="stat-label">Ignored Events</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalEvents">0</div>
                    <div class="stat-label">Total Events</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="sessionTime">00:00</div>
                    <div class="stat-label">Session Time</div>
                </div>
            </div>
        </div>

        <div class="events-section">
            <div class="events-title">📋 Event Log</div>
            <div class="event-log" id="eventLog">
                <div class="event-item">
                    <div class="event-time">Session Started</div>
                    <div class="event-description">Interview Smart Focus Detector initialized</div>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-interview" onclick="toggleInterviewMode()">🎯 Toggle Interview Mode</button>
            <button class="btn btn-primary" onclick="testDetection()">🧪 Test Detection</button>
            <button class="btn btn-secondary" onclick="clearLog()">🗑️ Clear Log</button>
            <button class="btn btn-secondary" onclick="downloadLog()">💾 Download Log</button>
        </div>
    </div>

    <script>
        class InterviewSmartFocusDetector {
            constructor() {
                this.events = [];
                this.suspiciousCount = 0;
                this.ignoredCount = 0;
                this.sessionStartTime = Date.now();
                this.originalTitle = document.title;
                this.interviewMode = true;
                this.strictMode = false;
                this.audioAlerts = false;
                this.whitelist = ['InterviewCracker', 'interviewcracker', 'mDNSResponder', 'System Preferences'];
                this.helperAppActive = false;
                this.lastFocusTime = Date.now();
                this.gracePeriod = 2000; // 2 seconds grace period for app switching
                
                this.init();
                this.startSessionTimer();
                this.startHelperDetection();
                this.loadSettings();
                this.renderWhitelist();
            }

            init() {
                // Page Visibility API - detects tab changes
                document.addEventListener('visibilitychange', () => {
                    this.handleVisibilityChange();
                });

                // Window focus/blur events - detects window switching
                window.addEventListener('focus', () => {
                    this.handleWindowFocus();
                });

                window.addEventListener('blur', () => {
                    this.handleWindowBlur();
                });

                // Settings event listeners
                document.getElementById('interviewMode').addEventListener('change', (e) => {
                    this.interviewMode = e.target.checked;
                    this.saveSettings();
                    this.updateInterviewBanner();
                    this.logEvent(`Interview mode ${this.interviewMode ? 'enabled' : 'disabled'}`, 'settings');
                });

                document.getElementById('strictMode').addEventListener('change', (e) => {
                    this.strictMode = e.target.checked;
                    this.saveSettings();
                    this.logEvent(`Strict mode ${this.strictMode ? 'enabled' : 'disabled'}`, 'settings');
                });

                document.getElementById('audioAlerts').addEventListener('change', (e) => {
                    this.audioAlerts = e.target.checked;
                    this.saveSettings();
                    this.logEvent(`Audio alerts ${this.audioAlerts ? 'enabled' : 'disabled'}`, 'settings');
                });

                // Additional detection for strict mode
                if (this.strictMode) {
                    document.addEventListener('mouseleave', () => {
                        if (this.strictMode) {
                            this.logEvent('Mouse left document area', 'mouse-leave');
                        }
                    });

                    document.addEventListener('mouseenter', () => {
                        if (this.strictMode) {
                            this.logEvent('Mouse entered document area', 'mouse-enter');
                        }
                    });
                }

                // Keyboard events for additional focus detection
                document.addEventListener('keydown', (e) => {
                    if (e.altKey && e.key === 'Tab') {
                        this.handleAltTab();
                    }
                });

                // Beforeunload event
                window.addEventListener('beforeunload', () => {
                    this.logEvent('User attempting to leave page', 'before-unload');
                });

                // Listen for helper app messages
                window.addEventListener('message', (event) => {
                    this.handleHelperMessage(event);
                });

                console.log('Interview Smart Focus Detector initialized');
            }

            startHelperDetection() {
                // Try to detect if InterviewCracker helper is running
                // This uses various methods to detect the helper app
                setInterval(() => {
                    this.checkHelperApp();
                }, 1000);
            }

            checkHelperApp() {
                // Method 1: Check for specific window titles
                const possibleHelperTitles = [
                    'InterviewCracker',
                    'mDNSResponder',
                    'System Preferences',
                    'Finder',
                    'Activity Monitor',
                    'Terminal'
                ];

                // Method 2: Try to communicate with the helper app via postMessage
                try {
                    // This will only work if the helper app is designed to listen
                    if (window.electronAPI) {
                        // If we're in an Electron context, we can communicate differently
                        this.helperAppActive = true;
                        this.updateInterviewStatus('Connected via Electron API', 'active');
                    } else {
                        // Fallback: assume helper is active during certain conditions
                        this.detectHelperByBehavior();
                    }
                } catch (error) {
                    this.detectHelperByBehavior();
                }
            }

            detectHelperByBehavior() {
                // Smart detection based on focus patterns
                const now = Date.now();
                const timeSinceLastFocus = now - this.lastFocusTime;
                
                // If focus was lost very briefly (< 500ms), it's likely the helper app
                if (timeSinceLastFocus < 500 && document.hidden) {
                    this.helperAppActive = true;
                    this.updateInterviewStatus('Helper app detected (behavior pattern)', 'active');
                } else if (!document.hidden && !document.hasFocus()) {
                    // Window is visible but not focused - could be helper app
                    this.helperAppActive = true;
                    this.updateInterviewStatus('Helper app likely active', 'active');
                } else {
                    this.helperAppActive = false;
                    this.updateInterviewStatus('No helper detected', 'inactive');
                }
            }

            handleHelperMessage(event) {
                // Handle messages from the helper app
                if (event.data && event.data.type === 'interviewcracker-helper') {
                    this.helperAppActive = event.data.active || false;
                    this.updateInterviewStatus(
                        event.data.active ? 'Helper app active' : 'Helper app inactive',
                        event.data.active ? 'active' : 'inactive'
                    );
                    this.logEvent(`Helper app status: ${event.data.active ? 'active' : 'inactive'}`, 'interview-helper');
                }
            }

            handleVisibilityChange() {
                const isHidden = document.hidden;
                const timestamp = new Date().toLocaleTimeString();
                const now = Date.now();
                
                if (isHidden) {
                    // Check if we should ignore this due to interview mode
                    if (this.shouldIgnoreEvent('tab-hidden')) {
                        this.ignoredCount++;
                        this.logEvent('Tab hidden - ignored (Interview mode)', 'whitelist-ignored');
                        return;
                    }

                    this.suspiciousCount++;
                    this.updateTabStatus('Hidden/Background', 'inactive', timestamp);
                    this.logEvent('Tab switched to background or minimized', 'tab-hidden');
                    document.title = '👋 Come back to your interview!';
                    document.body.classList.add('tab-inactive');
                    
                    if (this.audioAlerts) {
                        this.playAlert();
                    }
                } else {
                    this.updateTabStatus('Visible & Active', 'active', timestamp);
                    this.logEvent('Tab switched to foreground', 'tab-visible');
                    document.title = this.originalTitle;
                    document.body.classList.remove('tab-inactive');
                }
                
                this.lastFocusTime = now;
                this.updateStats();
            }

            handleWindowFocus() {
                const timestamp = new Date().toLocaleTimeString();
                this.updateWindowStatus('Focused', 'active', timestamp);
                this.logEvent('Window gained focus', 'window-focus');
                this.lastFocusTime = Date.now();
                this.updateStats();
            }

            handleWindowBlur() {
                const timestamp = new Date().toLocaleTimeString();
                const now = Date.now();
                
                // Check if we should ignore this due to interview mode
                if (this.shouldIgnoreEvent('window-blur')) {
                    this.ignoredCount++;
                    this.logEvent('Window focus lost - ignored (Interview mode)', 'whitelist-ignored');
                    return;
                }

                this.suspiciousCount++;
                this.updateWindowStatus('Not Focused', 'inactive', timestamp);
                this.logEvent('Window lost focus (switched to another app)', 'focus-lost');
                this.lastFocusTime = now;
                
                if (this.audioAlerts) {
                    this.playAlert();
                }
                
                this.updateStats();
            }

            handleAltTab() {
                if (this.shouldIgnoreEvent('alt-tab')) {
                    this.ignoredCount++;
                    this.logEvent('Alt+Tab detected - ignored (Interview mode)', 'whitelist-ignored');
                } else {
                    this.suspiciousCount++;
                    this.logEvent('Alt+Tab detected (attempting to switch)', 'alt-tab');
                    if (this.audioAlerts) {
                        this.playAlert();
                    }
                }
                this.updateStats();
            }

            shouldIgnoreEvent(eventType) {
                // Interview mode logic
                if (this.interviewMode && this.helperAppActive) {
                    return true;
                }

                // Grace period logic - ignore rapid focus changes
                const timeSinceLastFocus = Date.now() - this.lastFocusTime;
                if (timeSinceLastFocus < this.gracePeriod) {
                    return true;
                }

                return false;
            }

            updateTabStatus(status, type, timestamp) {
                const statusElement = document.getElementById('tabStatusValue');
                const timeElement = document.getElementById('tabStatusTime');
                const cardElement = document.getElementById('tabStatus');
                
                statusElement.textContent = status;
                statusElement.className = `status-value ${type}`;
                timeElement.textContent = `Last updated: ${timestamp}`;
                
                cardElement.className = `status-card ${type}`;
            }

            updateWindowStatus(status, type, timestamp) {
                const statusElement = document.getElementById('windowStatusValue');
                const timeElement = document.getElementById('windowStatusTime');
                const cardElement = document.getElementById('windowStatus');
                
                statusElement.textContent = status;
                statusElement.className = `status-value ${type}`;
                timeElement.textContent = `Last updated: ${timestamp}`;
                
                cardElement.className = `status-card ${type}`;
            }

            updateInterviewStatus(status, type) {
                const statusElement = document.getElementById('interviewStatusValue');
                const timeElement = document.getElementById('interviewStatusTime');
                const timestamp = new Date().toLocaleTimeString();
                
                statusElement.textContent = status;
                statusElement.className = `status-value ${type}`;
                timeElement.textContent = `Last updated: ${timestamp}`;
            }

            updateInterviewBanner() {
                const banner = document.getElementById('interviewBanner');
                if (this.interviewMode) {
                    banner.classList.add('active');
                } else {
                    banner.classList.remove('active');
                }
            }

            logEvent(description, type) {
                const timestamp = new Date().toLocaleTimeString();
                const event = {
                    time: timestamp,
                    description: description,
                    type: type,
                    fullTimestamp: new Date().toISOString()
                };
                
                this.events.push(event);
                this.addEventToDOM(event);
                this.updateStats();
            }

            addEventToDOM(event) {
                const eventLog = document.getElementById('eventLog');
                const eventItem = document.createElement('div');
                eventItem.className = `event-item ${event.type}`;
                
                eventItem.innerHTML = `
                    <div class="event-time">${event.time}</div>
                    <div class="event-description">${event.description}</div>
                `;
                
                eventLog.insertBefore(eventItem, eventLog.firstChild);
                
                // Limit to 50 events in the DOM
                while (eventLog.children.length > 50) {
                    eventLog.removeChild(eventLog.lastChild);
                }
                
                eventLog.scrollTop = 0;
            }

            updateStats() {
                document.getElementById('suspiciousCount').textContent = this.suspiciousCount;
                document.getElementById('ignoredCount').textContent = this.ignoredCount;
                document.getElementById('totalEvents').textContent = this.events.length;
            }

            startSessionTimer() {
                setInterval(() => {
                    const elapsed = Date.now() - this.sessionStartTime;
                    const minutes = Math.floor(elapsed / 60000);
                    const seconds = Math.floor((elapsed % 60000) / 1000);
                    document.getElementById('sessionTime').textContent = 
                        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }, 1000);
            }

            addToWhitelist() {
                const input = document.getElementById('whitelistInput');
                const value = input.value.trim();
                
                if (value && !this.whitelist.includes(value)) {
                    this.whitelist.push(value);
                    input.value = '';
                    this.renderWhitelist();
                    this.saveSettings();
                    this.logEvent(`Added "${value}" to whitelist`, 'settings');
                }
            }

            removeFromWhitelist(app) {
                this.whitelist = this.whitelist.filter(item => item !== app);
                this.renderWhitelist();
                this.saveSettings();
                this.logEvent(`Removed "${app}" from whitelist`, 'settings');
            }

            renderWhitelist() {
                const container = document.getElementById('whitelistTags');
                container.innerHTML = this.whitelist.map(app => `
                    <span class="whitelist-tag">
                        ${app}
                        <span class="remove-tag" onclick="detector.removeFromWhitelist('${app}')">×</span>
                    </span>
                `).join('');
            }

            saveSettings() {
                const settings = {
                    interviewMode: this.interviewMode,
                    strictMode: this.strictMode,
                    audioAlerts: this.audioAlerts,
                    whitelist: this.whitelist
                };
                localStorage.setItem('interviewDetectorSettings', JSON.stringify(settings));
            }

            loadSettings() {
                try {
                    const saved = localStorage.getItem('interviewDetectorSettings');
                    if (saved) {
                        const settings = JSON.parse(saved);
                        this.interviewMode = settings.interviewMode !== undefined ? settings.interviewMode : true;
                        this.strictMode = settings.strictMode || false;
                        this.audioAlerts = settings.audioAlerts || false;
                        this.whitelist = settings.whitelist || this.whitelist;
                        
                        // Update UI
                        document.getElementById('interviewMode').checked = this.interviewMode;
                        document.getElementById('strictMode').checked = this.strictMode;
                        document.getElementById('audioAlerts').checked = this.audioAlerts;
                        this.updateInterviewBanner();
                    }
                } catch (error) {
                    console.error('Failed to load settings:', error);
                }
            }

            playAlert() {
                // Create a subtle audio alert
                try {
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    const gain = audioContext.createGain();
                    
                    oscillator.connect(gain);
                    gain.connect(audioContext.destination);
                    
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    gain.gain.setValueAtTime(0.1, audioContext.currentTime);
                    gain.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
                    
                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.5);
                } catch (error) {
                    console.warn('Audio alert failed:', error);
                }
            }

            clearLog() {
                this.events = [];
                this.suspiciousCount = 0;
                this.ignoredCount = 0;
                const eventLog = document.getElementById('eventLog');
                eventLog.innerHTML = `
                    <div class="event-item">
                        <div class="event-time">Log Cleared</div>
                        <div class="event-description">Event log has been cleared</div>
                    </div>
                `;
                this.updateStats();
            }

            downloadLog() {
                const logData = {
                    sessionStartTime: new Date(this.sessionStartTime).toISOString(),
                    events: this.events,
                    settings: {
                        interviewMode: this.interviewMode,
                        strictMode: this.strictMode,
                        audioAlerts: this.audioAlerts,
                        whitelist: this.whitelist
                    },
                    stats: {
                        suspiciousCount: this.suspiciousCount,
                        ignoredCount: this.ignoredCount,
                        totalEvents: this.events.length
                    }
                };
                
                const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `interview-focus-log-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                this.logEvent('Focus detection log downloaded', 'download');
            }

            testDetection() {
                this.logEvent('Test event triggered manually', 'test');
                
                const container = document.querySelector('.container');
                container.classList.add('pulse');
                setTimeout(() => {
                    container.classList.remove('pulse');
                }, 2000);
            }

            toggleInterviewMode() {
                this.interviewMode = !this.interviewMode;
                document.getElementById('interviewMode').checked = this.interviewMode;
                this.saveSettings();
                this.updateInterviewBanner();
                this.logEvent(`Interview mode ${this.interviewMode ? 'enabled' : 'disabled'}`, 'settings');
            }
        }

        // Global functions
        function addToWhitelist() {
            detector.addToWhitelist();
        }

        function clearLog() {
            detector.clearLog();
        }

        function downloadLog() {
            detector.downloadLog();
        }

        function testDetection() {
            detector.testDetection();
        }

        function toggleInterviewMode() {
            detector.toggleInterviewMode();
        }

        // Initialize the detector
        let detector;
        document.addEventListener('DOMContentLoaded', () => {
            detector = new InterviewSmartFocusDetector();
        });

        // Add Enter key support for whitelist input
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('whitelistInput').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    addToWhitelist();
                }
            });
        });
    </script>
</body>
</html>
