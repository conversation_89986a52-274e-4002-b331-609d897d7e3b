// Content Injector - Injects helper directly into target websites
// This approach eliminates focus stealing issues entirely

class InterviewCrackerInjector {
  constructor() {
    this.isInjected = false;
    this.helperFrame = null;
    this.toggleButton = null;
    this.isVisible = false;
    this.init();
  }

  init() {
    console.log('🚀 InterviewCracker Content Injector initializing...');
    
    // Wait for page to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.inject());
    } else {
      this.inject();
    }

    // Listen for keyboard shortcuts
    this.setupKeyboardShortcuts();
  }

  inject() {
    if (this.isInjected) return;

    console.log('💉 Injecting InterviewCracker helper into page...');

    // Create the helper iframe
    this.createHelperFrame();
    
    // Create toggle button
    this.createToggleButton();
    
    // Mark as injected
    this.isInjected = true;
    
    console.log('✅ InterviewCracker helper injected successfully');
  }

  createHelperFrame() {
    this.helperFrame = document.createElement('iframe');
    this.helperFrame.id = 'interviewcracker-helper-frame';
    this.helperFrame.src = 'http://localhost:3000/helper'; // Adjust URL as needed
    
    // Style the iframe to be an overlay
    this.helperFrame.style.cssText = `
      position: fixed !important;
      top: 20px !important;
      right: 20px !important;
      width: 400px !important;
      height: 600px !important;
      border: 2px solid #4CAF50 !important;
      border-radius: 12px !important;
      background: white !important;
      box-shadow: 0 8px 32px rgba(0,0,0,0.3) !important;
      z-index: 999999 !important;
      display: none !important;
      resize: both !important;
      overflow: hidden !important;
      transition: all 0.3s ease !important;
    `;

    // Make it draggable
    this.makeDraggable(this.helperFrame);
    
    document.body.appendChild(this.helperFrame);
  }

  createToggleButton() {
    this.toggleButton = document.createElement('button');
    this.toggleButton.id = 'interviewcracker-toggle-btn';
    this.toggleButton.innerHTML = '🤖';
    this.toggleButton.title = 'Toggle InterviewCracker Helper (Ctrl+Shift+I)';
    
    this.toggleButton.style.cssText = `
      position: fixed !important;
      bottom: 20px !important;
      right: 20px !important;
      width: 50px !important;
      height: 50px !important;
      border-radius: 50% !important;
      background: linear-gradient(135deg, #4CAF50, #45a049) !important;
      border: none !important;
      color: white !important;
      font-size: 20px !important;
      cursor: pointer !important;
      z-index: 1000000 !important;
      box-shadow: 0 4px 16px rgba(76, 175, 80, 0.4) !important;
      transition: all 0.3s ease !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    `;

    // Add hover effects
    this.toggleButton.addEventListener('mouseenter', () => {
      this.toggleButton.style.transform = 'scale(1.1)';
      this.toggleButton.style.boxShadow = '0 6px 20px rgba(76, 175, 80, 0.6)';
    });

    this.toggleButton.addEventListener('mouseleave', () => {
      this.toggleButton.style.transform = 'scale(1)';
      this.toggleButton.style.boxShadow = '0 4px 16px rgba(76, 175, 80, 0.4)';
    });

    // Add click handler
    this.toggleButton.addEventListener('click', () => this.toggleHelper());

    document.body.appendChild(this.toggleButton);
  }

  makeDraggable(element) {
    let isDragging = false;
    let startX, startY, startLeft, startTop;

    const header = document.createElement('div');
    header.style.cssText = `
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      height: 30px !important;
      background: #4CAF50 !important;
      cursor: move !important;
      display: flex !important;
      align-items: center !important;
      padding: 0 10px !important;
      color: white !important;
      font-size: 12px !important;
      font-weight: bold !important;
    `;
    header.textContent = '🤖 InterviewCracker Helper';

    element.appendChild(header);

    header.addEventListener('mousedown', (e) => {
      isDragging = true;
      startX = e.clientX;
      startY = e.clientY;
      startLeft = parseInt(element.style.left) || 0;
      startTop = parseInt(element.style.top) || 0;
      
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
      e.preventDefault();
    });

    function onMouseMove(e) {
      if (!isDragging) return;
      
      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;
      
      element.style.left = (startLeft + deltaX) + 'px';
      element.style.top = (startTop + deltaY) + 'px';
      element.style.right = 'auto';
    }

    function onMouseUp() {
      isDragging = false;
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    }
  }

  toggleHelper() {
    if (!this.helperFrame) return;

    this.isVisible = !this.isVisible;
    
    if (this.isVisible) {
      this.helperFrame.style.display = 'block';
      this.toggleButton.style.background = 'linear-gradient(135deg, #ff6b6b, #ee5a52)';
      this.toggleButton.innerHTML = '❌';
      console.log('✅ InterviewCracker helper shown');
    } else {
      this.helperFrame.style.display = 'none';
      this.toggleButton.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
      this.toggleButton.innerHTML = '🤖';
      console.log('❌ InterviewCracker helper hidden');
    }
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl+Shift+I to toggle helper
      if (e.ctrlKey && e.shiftKey && e.key === 'I') {
        e.preventDefault();
        this.toggleHelper();
      }
      
      // Escape to hide helper
      if (e.key === 'Escape' && this.isVisible) {
        e.preventDefault();
        this.toggleHelper();
      }
    });
  }

  // Method to communicate with main app
  sendToMainApp(message) {
    try {
      // Try to communicate with the main InterviewCracker app
      window.postMessage({
        type: 'interviewcracker-injector',
        data: message,
        timestamp: Date.now()
      }, '*');
    } catch (error) {
      console.log('Could not communicate with main app:', error);
    }
  }
}

// Auto-initialize when script loads
if (typeof window !== 'undefined') {
  // Check if we're on an interview website
  const interviewSites = [
    'leetcode.com',
    'hackerrank.com',
    'codility.com',
    'codesignal.com',
    'pramp.com',
    'interviewing.io',
    'karat.com',
    'coderbyte.com'
  ];

  const currentDomain = window.location.hostname.toLowerCase();
  const isInterviewSite = interviewSites.some(site => 
    currentDomain.includes(site) || currentDomain.endsWith(site)
  );

  if (isInterviewSite || window.location.search.includes('interview')) {
    console.log('🎯 Interview website detected, initializing InterviewCracker injector...');
    new InterviewCrackerInjector();
  } else {
    console.log('ℹ️ Not an interview website, InterviewCracker injector not loaded');
  }
}

// Export for manual initialization
if (typeof module !== 'undefined' && module.exports) {
  module.exports = InterviewCrackerInjector;
}
